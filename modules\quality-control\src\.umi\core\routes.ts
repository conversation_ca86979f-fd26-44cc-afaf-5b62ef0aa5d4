// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+runtime@3.5.34_react@16.14.0/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@uni/components/src/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "exact": true,
    "redirect": "/main/analysis/hosp"
  },
  {
    "path": "/main/analysis/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__analysis__hosp2' */'@/pages/analysis/hosp2'), loading: LoadingComponent})
  },
  {
    "path": "/main/analysis/match",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__analysis__match' */'@/pages/analysis/match'), loading: LoadingComponent})
  },
  {
    "path": "/main/rule",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__rule__index' */'@/pages/rule/index'), loading: LoadingComponent})
  },
  {
    "path": "/main/customRule",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__customRule__index' */'@/pages/customRule/index'), loading: LoadingComponent})
  },
  {
    "path": "/main/details/type",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__details__type' */'@/pages/details/type'), loading: LoadingComponent})
  },
  {
    "path": "/main/details/casebook",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__details__casebook' */'@/pages/details/casebook'), loading: LoadingComponent})
  },
  {
    "path": "/main/efficiency",
    "exact": true,
    "redirect": "/main/efficiency/codeValueQuantification"
  },
  {
    "path": "/main/efficiency/codeValueQuantification",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__energyEfficiency__codeValueQuantification__index' */'@/pages/energyEfficiency/codeValueQuantification/index'), loading: LoadingComponent})
  },
  {
    "path": "/main/efficiency/codeWorkerLoad",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__energyEfficiency__codeWorkerLoad__index' */'@/pages/energyEfficiency/codeWorkerLoad/index'), loading: LoadingComponent})
  },
  {
    "path": "/doctor",
    "exact": true,
    "redirect": "/doctor/analysis/hosp"
  },
  {
    "path": "/doctor/analysis/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__doctor__analysis__hosp2' */'@/pages/doctor/analysis/hosp2'), loading: LoadingComponent})
  },
  {
    "path": "/doctor/analysis/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__doctor__analysis__dept2' */'@/pages/doctor/analysis/dept2'), loading: LoadingComponent})
  },
  {
    "path": "/doctor/details",
    "exact": true,
    "redirect": "/doctor/details/casebook"
  },
  {
    "path": "/doctor/details/casebook",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__doctor__details__casebook' */'@/pages/doctor/details/casebook'), loading: LoadingComponent})
  },
  {
    "path": "/doctor/details/type",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__doctor__details__type' */'@/pages/doctor/details/type'), loading: LoadingComponent})
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
