import { HeaderGroup } from '@tanstack/table-core/src/types';

export const getTooltipPopupContainer = (triggerNode: any) => {
  return triggerNode?.closest('#tanstack-table-container');
};

export const cellStyleProcessor = (column: any, tableInstance: any) => {
  return {
    position:
      column.getIsPinned() && column.columnDef.columnDefType !== 'group'
        ? 'sticky'
        : undefined,
    right:
      column.getIsPinned() === 'right'
        ? `${getTotalRight(tableInstance, column)}px`
        : undefined,
    left:
      column.getIsPinned() === 'left'
        ? `${getTotalLeft(tableInstance, column)}px`
        : undefined,
  };
};

export const cellClassNameProcessor = (column: any, tableInstance: any) => {
  let classNameArray = [];

  let pinned = column.getIsPinned();
  if (pinned === 'left' && column.columnDef.columnDefType !== 'group') {
    classNameArray.push('ant-table-cell-fix-left');
    if (
      column.getPinnedIndex() ===
      tableInstance.getLeftLeafHeaders()?.length - 1
    ) {
      classNameArray.push('ant-table-cell-fix-left-last');
    }
  }

  if (pinned === 'right' && column.columnDef.columnDefType !== 'group') {
    classNameArray.push('ant-table-cell-fix-right');
    if (column.getPinnedIndex() === 0) {
      classNameArray.push('ant-table-cell-fix-right-first');
    }
  }

  if (column.columnDef.meta?.align) {
    classNameArray.push(
      `cell-input-align-${column.columnDef.meta?.align ?? 'left'}`,
    );
  }

  if (column?.columnDef?.meta?.customClassName) {
    classNameArray.push(column?.columnDef?.meta?.customClassName);
  }

  return classNameArray;
};

export const getTotalRight = (tableInstance: any, column: any) => {
  let rightItems = tableInstance
    .getRightLeafHeaders()
    .slice(column.getPinnedIndex() + 1);

  let rightPixels = rightItems.reduce((acc, col) => acc + col.getSize(), 0);
  return rightPixels > 0 ? rightPixels + 8 : 0;
};

export const getTotalLeft = (tableInstance: any, column: any) => {
  let leftLeafHeaders = tableInstance.getLeftLeafHeaders();
  let currentColumnIndex = leftLeafHeaders.findIndex(
    (item) => item?.id === column?.id,
  );

  let leftItems = [];
  if (currentColumnIndex > -1) {
    leftItems = leftLeafHeaders.slice(0, currentColumnIndex);
  }

  let leftPixels = leftItems.reduce((acc, col) => acc + col.getSize(), 0);

  console.log(
    'leftLeafHeaders',
    leftLeafHeaders,
    currentColumnIndex,
    column,
    leftItems,
    leftPixels,
  );

  return leftPixels > 0 ? leftPixels : 0;
};

export const getTextAlign = (columnMeta: any) => {
  if (columnMeta?.align) {
    return columnMeta?.align;
  }

  if (columnMeta?.children?.length > 0) {
    return 'center';
  }

  return 'start';
};

export const getMergeHeaderGroups = (tableHeaderGroups: HeaderGroup[]) => {
  const headerGroups = tableHeaderGroups;
  const headerIds = new Set(); // 防止重复相同的列名
  const resultHeaderGroups = [];

  // 如果说只有一层 就直接返回
  if (headerGroups.length === 1) return tableHeaderGroups;

  for (let i = 0; i < headerGroups.length; i++) {
    const headerGroup =
      i === 0 ? headerGroups[i].headers : resultHeaderGroups[i];

    // 寻找相同对象
    const preHeaders = headerGroup.map((header) =>
      header.isPlaceholder
        ? {
            ...header,
            id: header?.column?.id,
            isPlaceholder: false,
            placeholderId: undefined,
            rowSpan: tableHeaderGroups.length - i,
            subHeaders: [],
          }
        : { ...header, rowSpan: 1 },
    );
    resultHeaderGroups.pop(); // 取出最后一个数组
    resultHeaderGroups.push(preHeaders); // 保存修改后的数组
    preHeaders.forEach((preHeader) => headerIds.add(preHeader.column.id));

    const targetHeaders = headerGroups[i + 1].headers;
    const newHeaders = targetHeaders.filter(
      (header) => !headerIds.has(header.column.id),
    );
    resultHeaderGroups.push(newHeaders);

    if (i === headerGroups.length - 2) {
      break;
    }
  }

  let results = [];

  resultHeaderGroups?.map((groups, index) => {
    results.push({
      depth: index,
      headers: groups,
      id: index?.toString(),
    });
  });

  return results;
};

export const bodyTheadStyle = (rowVirtualizer: any, virtualized?: boolean) => {
  let tbodyVirtualizedProps: any = {};
  if (virtualized) {
    tbodyVirtualizedProps = {
      style: {
        height: `${rowVirtualizer.getTotalSize()}px`, //tells scrollbar how big the table is
        position: 'relative', //needed for absolute positioning of rows
      },
    };
  }

  let theadVirtualizedProps: any = {};
  if (virtualized) {
    theadVirtualizedProps = {
      style: {
        position: 'sticky',
        top: 0,
        zIndex: 1,
      },
    };
  }

  let tableVirtualizeProps: any = {};
  if (virtualized) {
    tableVirtualizeProps = {
      style: {},
    };
  }

  return {
    tbody: tbodyVirtualizedProps,
    thead: theadVirtualizedProps,
    table: tableVirtualizeProps,
  };
};
