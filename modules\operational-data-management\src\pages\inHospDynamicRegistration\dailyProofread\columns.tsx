import React from 'react';
import { message, Space, Tooltip } from 'antd';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
  SaveOutlined,
  AlertOutlined,
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { ReqActionType } from '@/Constants';
import {
  CommonOdmEventConstants,
  DailyProofreadEventConstants,
} from './constants';
import { IDeptpatientAmtListItem } from './interface';
import IconBtn from '@uni/components/src/iconBtn/index';

/**
 * 创建操作列
 * @param editableKeys 当前编辑中的行keys
 * @returns 操作列定义
 */
export const createActionColumn = [
  {
    data: 'StatusName',
    dataIndex: 'StatusName',
    align: 'center',
    orderable: false,
    sorter: false,
    width: 50,
    render: (text, record) => {
      return (
        <Tooltip title={record?.StatusName}>
          {record?.Status === '100' ? (
            <CheckOutlined />
          ) : record?.Status === '1-99' || record?.Status === '99' ? (
            <CloseOutlined />
          ) : (
            <AlertOutlined />
          )}
        </Tooltip>
      );
    },
  },
  {
    data: 'IsLocked',
    dataIndex: 'IsLocked',
    align: 'center',
    orderable: false,
    sorter: false,
    render: (text, record) => {
      return record?.IsLocked ? '是' : '否';
    },
  },
  {
    dataIndex: 'option',
    title: '操作',
    visible: true,
    width: 90,
    align: 'center',
    valueType: 'option',
    fixed: 'right',
    render: (text: any, record: IDeptpatientAmtListItem) => {
      return (
        <Space size={15}>
          {/* 非编辑状态下显示编辑按钮 */}
          {
            <IconBtn
              type="edit"
              title="编辑"
              btnDisabled={record?.IsLocked}
              customIcon={<EditOutlined />}
              onClick={() => {
                // 触发编辑事件
                Emitter.emit(DailyProofreadEventConstants.ROW_EDIT, record);
              }}
            />
          }

          {/* 非编辑状态下显示锁定/解锁按钮 */}
          {/* {!record?.IsLocked && (
            <IconBtn
              type="lock"
              title="锁定"
              openPop={true}
              customIcon={<LockOutlined />}
              popOnConfirm={() => {
                // 触发锁定事件
                Emitter.emit(DailyProofreadEventConstants.ROW_LOCK, {
                  ids: [record.Id],
                  actionType: ReqActionType.Lock,
                });
              }}
            />
          )}

          {record?.IsLocked && (
            <IconBtn
              type="unlock"
              title="解锁"
              openPop={true}
              customIcon={<UnlockOutlined />}
              popOnConfirm={() => {
                // 触发解锁事件
                Emitter.emit(DailyProofreadEventConstants.ROW_UNLOCK, {
                  ids: [record.Id],
                  actionType: ReqActionType.Unlock,
                });
              }}
            />
          )} */}
        </Space>
      );
    },
  },
];

// 常量定义，以便在主组件中使用
export const ActionColumnWidth = 120;

export const detailColumns = [
  {
    dataIndex: 'operation',
    visible: true,
    width: 40,
    align: 'center',
    title: '',
    fixed: 'left',
    render: (node, record, index) => {
      return (
        <IconBtn
          title="查看病案首页"
          type="checkInfo"
          className="operation-btn"
          onClick={(e) => {
            if (record?.HisId) {
              (global?.window as any)?.eventEmitter?.emit('DMR_AOD_STATUS', {
                status: true,
                hisId: record.HisId,
              });
            } else {
              message.warn('HisId不存在');
            }
          }}
        />
      );
    },
  },
];
