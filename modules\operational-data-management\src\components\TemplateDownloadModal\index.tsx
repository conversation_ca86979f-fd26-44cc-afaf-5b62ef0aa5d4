import React, { useEffect } from 'react';
import { Modal, Form, message } from 'antd';
import { useRequest } from 'umi';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { isRespErr } from '@/utils/widgets';
import { DownloadTemplateFormItems } from './formItems';
import { UseDispostionEnum, downloadFile } from '@uni/utils/src/download';
import './styles.less';
import { uniCommonService } from '@uni/services/src/index';

// 请求类型常量
export enum TemplateActionType {
  GetTemplate = 'GetTemplate',
}

/**
 * API响应类型
 */
interface RespVO<T = any> {
  code: number;
  statusCode: number;
  data?: T;
  message?: string;
  success?: boolean;
  response?: any;
}

/**
 * 模板下载Modal组件的Props接口
 */
export interface TemplateDownloadModalProps {
  /** 是否显示modal */
  visible: boolean;
  /** 初始值 */
  initialValues?: Record<string, any>;
  /** API地址 */
  apiUrl: string;
  /** 取消回调 */
  onCancel: () => void;
  /** 成功回调 - 在API请求成功后调用 */
  onSuccess?: () => Promise<void>;
}

/**
 * 模板下载Modal组件
 * 用于下载Excel模板文件
 */
const TemplateDownloadModal: React.FC<TemplateDownloadModalProps> = ({
  visible,
  initialValues,
  apiUrl,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();

  // 处理表单初始值
  useEffect(() => {
    if (visible && initialValues) {
      // 默认设置文件名
      const formValues = {
        ...initialValues,
        fileName: initialValues.fileName || `模板_${new Date().getTime()}`,
      };

      // 使用setFieldsValue设置表单值
      form.setFieldsValue(formValues);
    }
  }, [visible, initialValues, form]);

  // 调用接口下载模板
  const { run, loading } = useRequest(
    (data) =>
      uniCommonService(apiUrl, {
        method: 'POST',
        data,
        dataType: 'dyn-ddr',
        requestType: 'json',
      }),
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: async (res, params) => {
        if (!isRespErr(res)) {
          // 调用downloadFile函数处理下载
          downloadFile(
            `${params[0]?.fileName}.xlsx`,
            res?.response,
            UseDispostionEnum.custom,
          );

          // 关闭modal
          onCancel();

          // 如果有传入onSuccess回调，则调用
          if (onSuccess) {
            await onSuccess();
          }
        } else {
          // API请求失败，显示错误信息
          message.error(res?.message || '下载模板失败');
        }
      },
      onError: () => {
        message.error('下载模板失败，请稍后重试');
      },
    },
  );

  // 表单提交处理
  const handleFinish = async (values: Record<string, any>) => {
    try {
      // 合并初始值和表单值
      const submitData = {
        ...initialValues,
        ...values,
      };
      await run(submitData);
    } catch (error) {
      console.error('提交表单错误:', error);
    }
  };

  return (
    <Modal
      open={visible}
      width={500}
      title="下载模板"
      destroyOnClose={true}
      onCancel={onCancel}
      confirmLoading={loading}
      onOk={() => form?.submit()}
      className="template-download-modal"
    >
      <ProFormContainer
        form={form}
        preserve={false}
        grid={true}
        labelCol={{}}
        wrapperCol={{}}
        searchOpts={DownloadTemplateFormItems}
        onFinish={handleFinish}
      />
    </Modal>
  );
};

export default TemplateDownloadModal;
