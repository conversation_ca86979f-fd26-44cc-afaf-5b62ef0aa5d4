import {
  ProForm,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormSelect,
} from '@uni/components/src/pro-form/index';
import dayjs from 'dayjs';

export const SpecialDiseaseDiscussionFormItems = (initValue, dictData) => [
  {
    dataType: 'text',
    title: '病案标识',
    placeholder: '病案号/姓名/住院号/条形码',
    name: 'keyWord',
  },
  {
    dataType: 'custom',
    name: 'DateRange.Group',
    render: (
      <ProForm.Group>
        <ProFormSelect
          name={'customDateType'}
          label="时间"
          options={[
            { label: '按出院时间', value: 'ByOutDate' },
            { label: '按结算时间', value: 'BySettleDate' },
          ]}
          initialValue={'ByOutDate'}
        />
        <ProFormDateRangePicker
          wrapperCol={{ offset: 6 }}
          {...{
            key: 'DateRange',
            name: 'DateRange',
            label: '',
            style: { width: '100%' },
            initialValue: initValue?.dateRange,
            required: true,
            transform: (value, namePath, allValues) => {
              return {
                Sdate: value ? dayjs(value[0]).format('YYYY-MM-DD') : undefined,
                Edate: value ? dayjs(value[1]).format('YYYY-MM-DD') : undefined,
              };
            },
            fieldProps: {
              style: { width: '100%' },
              format: 'YYYY-MM-DD',
              allowClear: false,
            },
          }}
        />
      </ProForm.Group>
    ),
  },
  {
    title: '医保类型',
    dataType: 'select',
    name: 'InsurTypes',
    initialValue: initValue?.InsurType,
    opts: dictData?.InsurType || [],
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
  },
  {
    title: '院区',
    dataType: 'select',
    name: 'HospCode',
    initialValue: initValue?.HospCode,
    opts: dictData?.Hospital || [],
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
  },
  {
    title: '科室',
    dataType: 'custom',
    name: 'CliDepts',
    render: (
      <ProFormDependency name={['HospCode']}>
        {({ HospCode }) => {
          let options =
            HospCode?.length > 0
              ? dictData?.Hierarchies?.filter(
                  (item) =>
                    HospCode?.includes(item.HospCode) &&
                    item.HierarchyType === '1',
                )
              : dictData?.Hierarchies?.filter(
                  (item) => item.HierarchyType === '1',
                );

          return (
            <ProFormSelect
              {...{
                key: 'CliDepts',
                name: 'CliDepts',
                label: '科室',
                initialValue: initValue?.CliDepts,
                opts: options || [],
                fieldProps: {
                  mode: 'multiple',
                  options: options,
                  maxTagCount: 'responsive',
                  fieldNames: {
                    label: 'Name',
                    value: 'Code',
                  },
                },
              }}
            />
          );
        }}
      </ProFormDependency>
    ),
  },
  {
    title: '责任医生',
    dataType: 'select',
    name: 'DoctorCodes',
    initialValue: initValue?.Doctors,
    opts: dictData?.Doctors || [],
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
  },
  {
    title: '分组',
    dataType: 'select',
    name: 'OriginChsDrgCodes',
    initialValue: initValue?.OriginChsDrgCodes,
    opts: [], // TODO
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
  },
  {
    title: '申诉状态',
    dataType: 'select',
    name: 'AppealTaskStatuses',
    initialValue: initValue?.OriginChsDrgCodes,
    opts: dictData?.AppealTaskStatus || [],
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
  },
];
