// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+runtime@3.5.34_react@16.14.0/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@uni/components/src/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "exact": true,
    "redirect": "/hqmsDataManagement"
  },
  {
    "path": "/hqmsCardImport",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__hqmsImportFile__index' */'@/pages/hqmsImportFile/index'), loading: LoadingComponent})
  },
  {
    "path": "/hqmsDataManagement",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__hqmsDataManagement__index' */'@/pages/hqmsDataManagement/index'), loading: LoadingComponent})
  },
  {
    "path": "/maintenanceDataCnt",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MaintenanceDataCnt__index' */'@/pages/MaintenanceDataCnt/index'), loading: LoadingComponent})
  },
  {
    "path": "/maintenanceRecordDataCnt",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__MaintenanceRecordDataCnt__index' */'@/pages/MaintenanceRecordDataCnt/index'), loading: LoadingComponent})
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
