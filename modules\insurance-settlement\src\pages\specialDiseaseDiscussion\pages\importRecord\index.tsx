import { useRequest } from 'umi';
import {
  Card,
  message,
  Button,
  Col,
  Row,
  Space,
  Divider,
  Dropdown,
  Modal,
  Radio,
} from 'antd';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useState,
  useRef,
} from 'react';
import { useModel } from '@@/plugin-model/useModel';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { useDeepCompareEffect } from 'ahooks';
import {
  InitTableState,
  TableAction,
  tableReducer,
} from '@uni/reducers/src/index';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import _ from 'lodash';
import ProFormContainer from '@uni/components/src/pro-form-container/index';
import { ImportRecordSearchFormItems } from './formItems';
import { ImportRecordColumns } from './columns';
import { ProForm, ProFormInstance } from '@uni/components/src/pro-form/index';
import dayjs from 'dayjs';
import { isEmptyValues } from '@uni/utils/src/utils';
import { Emitter } from '@uni/utils/src/emitter';
import { ImportRecordEventConstants } from './constants';
import DetailDrawer from './components/detailDrawer/index';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';
import { SpecialDiseaseDiscussionColumns } from '../../columns';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { downloadFile, UseDispostionEnum } from '@uni/utils/src/download';

// hack 获取 sessionStorage
const getSessionStorage = (key: string) => {
  return JSON.parse(sessionStorage.getItem(key) as string);
};

const ImportRecord = () => {
  const {
    globalState: { searchParams, dictData },
    setQiankunGlobalState,
  } = useModel('@@qiankunStateFromMaster');

  const [searchedValue, setSearchedValue] = useState(() => {
    // 处理日期范围，提取开始和结束日期
    const formatDateRange = (dateRange?: any[]) => {
      if (!dateRange) return { Sdate: undefined, Edate: undefined };

      return {
        Sdate: dayjs(dateRange[0]).startOf('M').format('YYYY-MM-DD'),
        Edate: dayjs(dateRange[1]).endOf('M').format('YYYY-MM-DD'),
      };
    };

    // 如果有搜索参数，优先使用
    if (!_.isEmpty(searchParams)) {
      return {
        ...searchParams,
        ...formatDateRange(searchParams?.dateRange),
      };
    }

    // 否则从sessionStorage获取
    const storedOpts = getSessionStorage('searchOpts') || {};
    return {
      ..._.omit(storedOpts, 'hospCode', 'hospCodes'),
      ...formatDateRange(storedOpts?.dateRange),
      HospCode: storedOpts?.hospCodes,
    };
  });

  const SearchFormItems = useMemo(() => {
    console.log(
      'useMemo',
      searchedValue,
      searchParams,
      ImportRecordSearchFormItems(searchedValue, dictData),
    );
    return ImportRecordSearchFormItems(searchedValue, dictData);
  }, [dictData, searchedValue]);

  const [proForm] = ProForm.useForm<ProFormInstance>();
  // drawer
  const [drawerVisible, setDrawerVisible] = useState({
    visible: false,
    record: null,
  });

  const [MainTableState, MainTableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, { ...InitTableState });

  // 添加状态来跟踪选中的通道类型
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  // 添加状态控制模态框显示
  const [modalVisible, setModalVisible] = useState(false);
  // 添加状态记录当前操作类型
  const [currentAction, setCurrentAction] = useState<'download' | 'import'>(
    'download',
  );

  // 获取表格列定义
  const { run: columnsReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/CenterSettle/AppealTaskImportResult/GetResultMasters',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          return response?.data?.Columns;
        } else {
          return [];
        }
      },
      onSuccess(data) {
        MainTableDispatch({
          type: TableAction.columnsChange,
          payload: {
            columns: tableColumnBaseProcessor(ImportRecordColumns, data),
          },
        });
      },
    },
  );

  // 轮询定时器引用
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 清除轮询定时器
  const clearPollingTimer = () => {
    if (pollingTimerRef.current) {
      clearTimeout(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }
  };

  // 检查是否需要轮询
  const checkNeedPolling = (dataList: any[]) => {
    return dataList.some(
      (item) => item?.ImportStatus === '1' || item?.ImportStatus === '正在执行',
    );
  };

  // 获取导入记录列表
  const {
    loading: listLoading,
    run: fetchList,
    data: listData,
  } = useRequest(
    (data, pagi, sorter = null) => {
      return uniCommonService(
        'Api/CenterSettle/AppealTaskImportResult/GetResultMasters',
        {
          method: 'POST',
          data: {
            DtParam: {
              Draw: 1,
              Start: (pagi.cur - 1) * pagi.size,
              Length: pagi.size,
            },
            ...data,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess(res, params) {
        if (res.code === 0) {
          const dataList = res?.data?.data ?? [];

          MainTableDispatch({
            type: TableAction.dataPagiChange,
            payload: {
              data: dataList,
              backPagination: {
                ...MainTableState.backPagination,
                current: params?.at(1)?.cur,
                pageSize: params?.at(1)?.size,
                total: res?.data?.recordsTotal ?? 0,
              },
            },
          });

          // 处理轮询逻辑
          clearPollingTimer(); // 先清除现有定时器

          // 检查是否需要轮询
          if (checkNeedPolling(dataList)) {
            // 如果有任务正在执行，设置3秒后轮询
            pollingTimerRef.current = setTimeout(() => {
              fetchList(params[0], params[1]);
            }, 3000);
          }
        } else {
        }
      },
    },
  );

  // 重启任务 restart
  const { loading: restartMissionReqLoading, run: restartMissionReq } =
    useRequest(
      (data) => {
        return uniCommonService('Api/CenterSettle/AppealTaskImport/Restart', {
          method: 'POST',
          params: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<any>) => {
          return response;
        },
        onSuccess(res, params) {
          if (res.code === 0) {
            message.success('重启任务成功');
            fetchList(searchedValue, {
              cur: 1,
              size:
                MainTableState.backPagination?.pageSize ??
                MainTableState.backPagination?.defaultPageSize,
            });
          } else {
            message.error('重启任务失败');
          }
        },
      },
    );

  useEffect(() => {
    if (searchedValue?.Sdate) {
      fetchList(searchedValue, {
        cur: 1,
        size:
          MainTableState.backPagination?.pageSize ??
          MainTableState.backPagination?.defaultPageSize,
      });
    }
  }, [searchedValue]);

  // 处理分页变化
  const backTableOnChange = (pagi: any) => {
    fetchList(
      {
        ...searchedValue,
      },
      { cur: pagi.current, size: pagi.pageSize },
    );
  };

  useEffect(() => {
    Emitter.on(ImportRecordEventConstants.CHECK_DETAIL, (record) => {
      setDrawerVisible({
        visible: true,
        record: record,
      });
    });

    Emitter.on(ImportRecordEventConstants.RESTART_MISSION, (record) => {
      restartMissionReq({ masterId: record?.Id });
    });

    return () => {
      Emitter.off(ImportRecordEventConstants.CHECK_DETAIL);
      Emitter.off(ImportRecordEventConstants.RESTART_MISSION);
      // 清除轮询定时器，防止内存泄漏
      clearPollingTimer();
    };
  }, []);

  // TODO 有时间跟management一起整合到外部component
  // 下载导入模板
  const { run: downloadTemplateReq } = useRequest(
    (appealChannel) => {
      return uniCommonService(
        'Api/CenterSettle/AppealTaskImport/GetSpecialCaseImportTemplate',
        {
          method: 'POST',
          data: {
            AppealChannel: appealChannel,
          },
          responseType: 'json',
        },
      );
    },
    {
      manual: true,
      formatResult: (response) => {
        return response;
      },
      onSuccess: (data, params) => {
        if (data.code === 0 && data.statusCode === 200) {
          message.success('Excel模板导出成功');
          let exportName = params?.[0]?.NormalChannel
            ? '特病单议病例普通申报模板'
            : '特病单议病例绿色通道模板';
          downloadFile(
            exportName,
            data?.response,
            UseDispostionEnum.combineWithNameFront,
          );
        } else {
          message.success('Excel模板导出失败，请联系管理员');
        }
      },
    },
  );

  const handleDownloadTemplate = (appealChannel) => {
    downloadTemplateReq(appealChannel);
  };

  // 导入文件
  const { run: importFileReq } = useRequest(
    (params: { file: File; appealChannel: string }) => {
      const formData = new FormData();
      formData.append('File', params.file);
      formData.append('AppealChannel', params.appealChannel);

      return uniCommonService(
        'Api/CenterSettle/AppealTaskImport/ImportSpecialCase',
        {
          method: 'POST',
          data: formData,
          requestType: 'form',
        },
      );
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res.code === 0 && res.statusCode === 200) {
          return 'success';
        } else {
          return 'failed';
        }
      },
      onSuccess: (data) => {
        if (data === 'success') {
          message.success('导入成功');
          // 刷新表格数据
          fetchList(searchedValue, {
            cur: 1,
            size:
              MainTableState.backPagination?.pageSize ??
              MainTableState.backPagination?.defaultPageSize,
          });
        } else {
          message.error(data?.message || '导入失败');
        }
      },
    },
  );

  // 显示选择通道类型的模态框
  const showChannelSelectModal = (action: 'download' | 'import') => {
    // 重置选中的通道
    setSelectedChannel(null);
    setCurrentAction(action);
    setModalVisible(true);
  };

  // 处理确认按钮点击
  const handleModalOk = () => {
    if (!selectedChannel) {
      message.warning('请先选择通道类型');
      return;
    }

    if (currentAction === 'download') {
      handleDownloadTemplate(selectedChannel);
    } else {
      // 创建一个隐藏的上传组件并触发点击
      const uploadInput = document.createElement('input');
      uploadInput.type = 'file';
      uploadInput.accept = '.xlsx';
      uploadInput.style.display = 'none';
      uploadInput.onchange = (e) => {
        const file = e.target.files?.[0];
        if (file) {
          importFileReq({
            file,
            appealChannel: selectedChannel,
          });
        }
        // 移除临时创建的元素
        document.body.removeChild(uploadInput);
      };
      document.body.appendChild(uploadInput);
      uploadInput.click();
    }
    setModalVisible(false);
  };

  // 处理取消按钮点击
  const handleModalCancel = () => {
    setModalVisible(false);
  };

  return (
    <Row
      wrap={false}
      gutter={8}
      style={{ overflow: 'hidden' }}
      className="special_disease_discussion_container"
    >
      <Col flex="330px">
        <Card
          title="查询条件"
          className="special_disease_discussion_search_card"
        >
          <ProFormContainer
            className={'search_form noMarginBottom'}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            preserve={false}
            grid
            searchOpts={SearchFormItems as any}
            submitter={{
              render: (props, doms) => {
                return [
                  <Button
                    type="primary"
                    style={{
                      width: '100%',
                      marginTop: '8px',
                      borderRadius: 0,
                    }}
                    key="submit"
                    onClick={() => {
                      props.form?.submit?.();
                    }}
                  >
                    查询
                  </Button>,
                ];
              },
            }}
            onFinish={async (values) => {
              // console.log('onFinish', values);
              setSearchedValue(values);
              // setQiankunGlobalState({
              //   dictData: dictData,
              //   searchParams: {
              //     ...searchParams,
              //     ..._.omit(values, ['Sdate', 'Edate']),
              //     // dateRange: [values.Sdate, values.Edate],
              //   },
              // });
            }}
          />
        </Card>
      </Col>
      <Col flex="auto">
        <Card
          title="上报记录列表"
          extra={
            <Space>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'download',
                      label: (
                        // <div style={{ padding: '6px' }}>下载导入用模板</div>
                        <Button
                          key="getTemplate"
                          type="text"
                          icon={<DownloadOutlined />}
                          onClick={(e) => {
                            showChannelSelectModal('download');
                            // Emitter.emit(
                            //   SpecialDiseaseDiscussionEventConstants.SHOW_TEMPLATE_MODAL,
                            //   'download',
                            // );
                          }}
                        >
                          模板
                        </Button>
                      ),
                      // onClick: () => {
                      //   showChannelSelectModal('download');
                      // },
                    },
                    {
                      key: 'import',
                      label: (
                        <Button
                          key="getTemplate"
                          type="text"
                          icon={<UploadOutlined />}
                          onClick={(e) => {
                            showChannelSelectModal('import');
                            // Emitter.emit(
                            //   SpecialDiseaseDiscussionEventConstants.SHOW_TEMPLATE_MODAL,
                            //   'import',
                            // );
                          }}
                        >
                          导入
                        </Button>
                      ),
                      // onClick: () => {
                      //   showChannelSelectModal('import');
                      // },
                    },
                  ],
                }}
              >
                <Button>导入</Button>
              </Dropdown>
              <Divider type="vertical" />
              <ExportIconBtn
                isBackend={true}
                backendObj={{
                  url: 'Api/CenterSettle/AppealTaskImportResult/ExportGetResultMasters',
                  method: 'POST',
                  data: {
                    ...searchedValue,
                  },
                  fileName: '上报记录列表',
                }}
                btnDisabled={MainTableState?.data?.length < 1}
              />
              <TableColumnEditButton
                {...{
                  columnInterfaceUrl:
                    'Api/CenterSettle/AppealTaskImportResult/GetResultMasters',
                  onTableRowSaveSuccess: (newColumns) => {
                    MainTableDispatch({
                      type: TableAction.columnsChange,
                      payload: {
                        columns: tableColumnBaseProcessor(
                          ImportRecordColumns,
                          newColumns,
                        ),
                      },
                    });
                  },
                }}
              />
            </Space>
          }
        >
          <UniTable
            isBackPagination
            id="import_record_list"
            columns={MainTableState?.columns}
            dataSource={MainTableState?.data}
            loading={listLoading}
            rowKey="id"
            pagination={MainTableState.backPagination}
            onChange={backTableOnChange}
            scroll={{ x: 'max-content' }}
          />
        </Card>

        <DetailDrawer
          visible={drawerVisible.visible}
          record={drawerVisible.record}
          onClose={() => {
            setDrawerVisible({
              visible: false,
              record: null,
            });
          }}
        />

        {/* 模板 */}
        <Modal
          title="模板内容"
          open={modalVisible}
          onOk={handleModalOk}
          onCancel={handleModalCancel}
        >
          <div>
            请选择模板类型：
            <Radio.Group
              onChange={(e) => setSelectedChannel(e.target.value)}
              value={selectedChannel}
            >
              {dictData?.CenterSettleAppealChannel?.map((item) => (
                <Radio key={item.Code} value={item.Code}>
                  {item.Name}
                </Radio>
              ))}
            </Radio.Group>
          </div>
        </Modal>
      </Col>
    </Row>
  );
};

export default ImportRecord;
