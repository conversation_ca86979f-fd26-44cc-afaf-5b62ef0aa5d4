import IconBtn from '@uni/components/src/iconBtn/index';
import { Emitter } from '@uni/utils/src/emitter';
import { Space } from 'antd';
import { SpecialDiseaseDiscussionEventConstants } from './constants';

export const SpecialDiseaseDiscussionColumns = [
  {
    dataIndex: 'option',
    title: '',
    visible: true,
    width: 60,
    align: 'center',
    valueType: 'option',
    fixed: 'left',
    render: (text, record: any) => (
      <Space size={12}>
        <IconBtn
          type="edit"
          title="填写申诉内容"
          className="inpatients_blue-color"
          onClick={() => {
            Emitter.emit(
              SpecialDiseaseDiscussionEventConstants.EDIT_APPEAL_CLK,
              record,
            );
          }}
        />
        <IconBtn
          type="delete"
          title="删除"
          popTitle="删除"
          openPop
          popOnConfirm={() => {
            Emitter.emit(
              SpecialDiseaseDiscussionEventConstants.DELETE_CLK,
              record,
            );
          }}
        />
      </Space>
    ),
  },
];
