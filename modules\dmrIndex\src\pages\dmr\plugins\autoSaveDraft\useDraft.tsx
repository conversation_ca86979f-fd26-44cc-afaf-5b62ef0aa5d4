// useDraft.tsx
import { useEffect, useRef, useCallback, useState } from 'react';
import DraftPlugin from './index';

interface UseDraftOptions {
  hisId: string;
  autoSave?: boolean;
  debounceDelay?: number;
}

interface UseDraftReturn {
  saveDraft: (content: any) => void;
  saveImmediately: (content: any) => Promise<void>;
  getDraft: () => Promise<any>;
  deleteDraft: () => Promise<void>;
  validateDraft: (dmrLatestDate: string) => Promise<void>;
  isLoading: boolean;
}

export const useDraft = (options: UseDraftOptions): UseDraftReturn => {
  const { hisId, autoSave = true } = options;
  const draftPluginRef = useRef<DraftPlugin | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const previousContentRef = useRef<string>('');

  // 初始化DraftPlugin
  useEffect(() => {
    if (!hisId) return;

    const plugin = new DraftPlugin();
    plugin.setHisId(hisId);
    draftPluginRef.current = plugin;

    // 等待worker初始化完成
    const checkReady = () => {
      setTimeout(() => {
        setIsLoading(false);
      }, 100);
    };
    checkReady();

    return () => {
      plugin.destroy();
      draftPluginRef.current = null;
    };
  }, [hisId]);

  // 页面卸载时立即保存
  useEffect(() => {
    const handleBeforeUnload = async () => {
      if (draftPluginRef.current && previousContentRef.current) {
        // 使用sendBeacon或同步方式确保数据保存
        try {
          await draftPluginRef.current.saveImmediately(
            JSON.parse(previousContentRef.current)
          );
        } catch (error) {
          console.error('Failed to save on unload:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      handleBeforeUnload(); // 组件卸载时也保存
    };
  }, []);

  const saveDraft = useCallback((content: any) => {
    if (!draftPluginRef.current) return;
    
    const contentString = JSON.stringify(content);
    
    // 避免重复保存相同内容
    if (contentString === previousContentRef.current) {
      return;
    }
    
    previousContentRef.current = contentString;
    draftPluginRef.current.saveDraftToLocal(content);
  }, []);

  const saveImmediately = useCallback(async (content: any): Promise<void> => {
    if (!draftPluginRef.current) return;
    
    const contentString = JSON.stringify(content);
    previousContentRef.current = contentString;
    
    await draftPluginRef.current.saveImmediately(content);
  }, []);

  const getDraft = useCallback(async (): Promise<any> => {
    if (!draftPluginRef.current) return {};
    return await draftPluginRef.current.getDraft();
  }, []);

  const deleteDraft = useCallback(async (): Promise<void> => {
    if (!draftPluginRef.current) return;
    previousContentRef.current = '';
    await draftPluginRef.current.deleteDraft();
  }, []);

  const validateDraft = useCallback(async (dmrLatestDate: string): Promise<void> => {
    if (!draftPluginRef.current) return;
    await draftPluginRef.current.validateDraft(dmrLatestDate);
  }, []);

  return {
    saveDraft,
    saveImmediately,
    getDraft,
    deleteDraft,
    validateDraft,
    isLoading
  };
};