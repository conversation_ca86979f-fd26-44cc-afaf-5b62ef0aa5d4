import { SorterResult } from 'antd/lib/table/interface';
import _ from 'lodash';
import { v4 as uuidv4 } from 'uuid';

export const codeSdateEdateProcessor = (data: any, useCodeDate: boolean) => {
  // 表示用的是 登记时间 所以用的是CodeSdate CodeEdate
  if (useCodeDate === true) {
    data['CodeSdate'] = data['Sdate'] ?? data['CodeSdate'];
    data['CodeEdate'] = data['Edate'] ?? data['CodeEdate'];

    delete data?.['Sdate'];
    delete data?.['Edate'];
  } else {
    delete data?.['CodeSdate'];
    delete data?.['CodeEdate'];
  }
};

export const codeSdateEdateProcessorViaSearchedValue = (searchedValue: any) => {
  codeSdateEdateProcessor(searchedValue, searchedValue?.UseRegistDate === true);

  return searchedValue;
};

export const dataTableFilterSorterHandler = (
  columns,
  filtData = {},
  sortData: SorterResult<any> = {},
) => {
  let columnDataObj = [];
  let orderDataObj = [];
  let orderDataIndex = 0;

  columns.forEach((col: any, index) => {
    // sort
    let temp = sortData?.columnKey === col.data;
    if (temp) {
      orderDataObj[orderDataIndex] = {
        column: index,
        dir: sortData.order === 'ascend' ? 'asc' : 'desc',
      };
      orderDataIndex++;
    }
    // filt
    let temp2 = filtData[col.data];
    columnDataObj[index] = {
      data: col.data,
      name: col.name,
      searchable: temp2 ? true : false,
      orderable: temp,
      search: {
        // ...forUmiRequestFormData
        value: temp2 ? (Array.isArray(temp2) ? temp2.join(',') : temp2) : '',
        regex: false,
      },
    };
  });

  return { columns: columnDataObj, order: orderDataObj };
};

export const isEmptyObj = (obj) => {
  let isEmpty = true;
  if (!obj || Object.keys(obj)?.length === 0) return isEmpty;
  Object.keys(obj).forEach((d) => {
    if (obj[d] != null && obj[d] !== '') {
      isEmpty = false;
    }
  });
  return isEmpty;
};

export const toArrayMode = (value) => {
  return Array.isArray(value)
    ? value
    : _.isString(value?.toString())
    ? [value]
    : [];
};

// 示踪操作api额外处理
export const handleMrActionApi = (res) => {
  // 成功返回的接口才会进入
  if (res.StatusCode === '200') {
    return {
      data: res.Data?.map((d) => ({ ...d, isCorrect: true, uuid: uuidv4() })),
      isCorrect: true,
    };
  } else if (res.StatusCode === '400') {
    if (res.Data?.length > 0) {
      return {
        data: res.Data?.map((d) => ({
          ...d,
          isCorrect: false,
          // 做一个容错处理
          errMsg: d?.ErrMsg ? [d?.ErrMsg] : [res.ErrMsg],
          uuid: uuidv4(),
        })),
        isCorrect: false,
        // 做一个容错处理
        errMsg: res.Data?.at(0)?.ErrMsg
          ? res.Data?.map((d) => d.ErrMsg)
          : [res.ErrMsg],
        errType: res.StatusCode,
      };
    }
    return {
      data: [],
      isCorrect: false,
      errMsg: [res.ErrMsg],
      errType: res.StatusCode,
    };
  } else if (res.StatusCode === '404') {
    return {
      data: [],
      isCorrect: false,
      errMsg: [res.ErrMsg],
      errType: res.StatusCode,
    };
  }
};
