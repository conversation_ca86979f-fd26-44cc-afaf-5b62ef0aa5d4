// @ts-nocheck
import { plugin } from './plugin';
import * as Plugin_0 from '../../app.tsx';
import * as Plugin_1 from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/modules/drg-data-manager/src/.umi/plugin-dva/runtime.tsx';
import * as Plugin_2 from '../plugin-initial-state/runtime';
import * as Plugin_3 from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/modules/drg-data-manager/src/.umi/plugin-locale/runtime.tsx';
import * as Plugin_4 from '../plugin-model/runtime';
import * as Plugin_5 from '@@/plugin-qiankun/slaveRuntimePlugin';
import * as Plugin_6 from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/packages/commons/src/plugins/on-route-change.ts';

  plugin.register({
    apply: Plugin_0,
    path: '../../app.tsx',
  });
  plugin.register({
    apply: Plugin_1,
    path: 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/modules/drg-data-manager/src/.umi/plugin-dva/runtime.tsx',
  });
  plugin.register({
    apply: Plugin_2,
    path: '../plugin-initial-state/runtime',
  });
  plugin.register({
    apply: Plugin_3,
    path: 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/modules/drg-data-manager/src/.umi/plugin-locale/runtime.tsx',
  });
  plugin.register({
    apply: Plugin_4,
    path: '../plugin-model/runtime',
  });
  plugin.register({
    apply: Plugin_5,
    path: '@@/plugin-qiankun/slaveRuntimePlugin',
  });
  plugin.register({
    apply: Plugin_6,
    path: 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/packages/commons/src/plugins/on-route-change.ts',
  });

export const __mfsu = 1;
