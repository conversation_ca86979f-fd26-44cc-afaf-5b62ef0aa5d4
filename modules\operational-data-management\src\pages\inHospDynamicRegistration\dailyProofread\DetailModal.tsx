import React, { useEffect, useReducer } from 'react';
import { Modal, Table, Spin, Space, Divider } from 'antd';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src/commonService';
import { TableAction, tableReducer, InitTableState } from '@uni/reducers/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { isRespErr, RespType } from '@/utils/widgets';
import { UniTable } from '@uni/components/src/index';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';
import { detailColumns } from './columns';
import { v4 as uuidv4 } from 'uuid';

interface DetailModalProps {
  visible: boolean;
  onCancel: () => void;
  apiData: any;
  title: string;
}

const DetailModal: React.FC<DetailModalProps> = ({
  visible,
  onCancel,
  apiData,
  title,
}) => {
  // 使用 tableReducer 管理表格状态
  const [TableState, TableDispatch] = useReducer(tableReducer, InitTableState);

  // 获取表格列定义
  const { loading: columnsLoading, run: fetchColumns } = useRequest(
    () =>
      uniCommonService('Api/Dyn-ddr/DmrCardQuery/GetOutHospCards', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          TableDispatch({
            type: TableAction.columnsChange,
            payload: {
              columns: tableColumnBaseProcessor(
                detailColumns,
                res?.data?.Columns,
                'local',
              ),
            },
          });
          return res.data?.Columns;
        }
        return null;
      },
    },
  );

  // 获取表格数据
  const {
    data: tableData,
    loading: dataLoading,
    run: fetchData,
  } = useRequest(
    (data) =>
      uniCommonService('Api/Dyn-ddr/DmrCardQuery/GetOutHospCards', {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespType<any>) => {
        if (!isRespErr(res)) {
          TableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: res?.data?.data?.map((d) => ({
                ...d,
                uuid: uuidv4(),
              })),
            },
          });
          return res.data;
        }
        return null;
      },
    },
  );

  // 当 Modal 显示或参数变化时获取数据
  useEffect(() => {
    if (visible && apiData) {
      fetchColumns();
      fetchData(apiData);
    }
  }, [visible, apiData]);

  return (
    <Modal
      title={
        <>
          <Space>
            <span>{title}</span>
            <Divider type="vertical" />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: 'Api/Dyn-ddr/DmrCardQuery/GetOutHospCards',
                onTableRowSaveSuccess: (newColumns) => {
                  TableDispatch({
                    type: TableAction.columnsChange,
                    payload: {
                      columns: tableColumnBaseProcessor(
                        detailColumns,
                        newColumns,
                      ),
                    },
                  });
                },
              }}
            />
          </Space>
        </>
      }
      open={visible}
      onCancel={onCancel}
      width={1000}
      footer={null}
      destroyOnClose
    >
      <Spin spinning={columnsLoading || dataLoading}>
        <UniTable
          id={'DetailModal-table'}
          rowKey="uuid"
          columns={TableState.columns}
          dataSource={TableState.data}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 'max-content' }}
        />
      </Spin>
    </Modal>
  );
};

export default DetailModal;
