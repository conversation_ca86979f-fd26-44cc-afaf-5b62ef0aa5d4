// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+runtime@3.5.34_react@16.14.0/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@uni/components/src/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "exact": true,
    "redirect": "/quality/hosp"
  },
  {
    "path": "/quality/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__quality__hosp__index2' */'@/pages/quality/hosp/index2'), loading: LoadingComponent})
  },
  {
    "path": "/quality/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__quality__dept__index' */'@/pages/quality/dept/index'), loading: LoadingComponent})
  },
  {
    "path": "/quality/medTeam",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__quality__medTeam__index' */'@/pages/quality/medTeam/index'), loading: LoadingComponent})
  },
  {
    "path": "/sd/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__sd__hosp__index2' */'@/pages/sd/hosp/index2'), loading: LoadingComponent})
  },
  {
    "path": "/sd/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__sd__dept__index' */'@/pages/sd/dept/index'), loading: LoadingComponent})
  },
  {
    "path": "/sd/medTeam",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__sd__medTeam__index' */'@/pages/sd/medTeam/index'), loading: LoadingComponent})
  },
  {
    "path": "/complication/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__complication__hosp__index2' */'@/pages/complication/hosp/index2'), loading: LoadingComponent})
  },
  {
    "path": "/complication/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__complication__dept__index' */'@/pages/complication/dept/index'), loading: LoadingComponent})
  },
  {
    "path": "/complication/medTeam",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__complication__medTeam__index' */'@/pages/complication/medTeam/index'), loading: LoadingComponent})
  },
  {
    "path": "/serviceability/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__serviceability__hosp__index2' */'@/pages/serviceability/hosp/index2'), loading: LoadingComponent})
  },
  {
    "path": "/serviceability/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__serviceability__dept__index2' */'@/pages/serviceability/dept/index2'), loading: LoadingComponent})
  },
  {
    "path": "/serviceability/medTeam",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__serviceability__medTeam__index' */'@/pages/serviceability/medTeam/index'), loading: LoadingComponent})
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
