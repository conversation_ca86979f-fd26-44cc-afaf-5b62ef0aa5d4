import {
  departmentTransferColumns,
  icdeColumns,
  icuColumns,
  operationColumns,
  pathologyIcdeColumns,
  tcmIcdeColumns,
} from '@/pages/doubleDeck/components/columns';
import pick from 'lodash/pick';
import Diff from 'diff';
import { isEmptyValues } from '@uni/utils/src/utils';

export const TableDiffKeys = {
  'diagnosis-table': ['diagnosis-table'],
  'operation-table': ['operation-table'],
  'pathological-diagnosis-table': ['pathological-diagnosis-table'],
  'icu-table': ['icu-table'],

  'tcm-diagnosis-table': ['tcm-diagnosis-table'],

  diagnosisTable: ['diagnosis-table'],
  operationTable: ['operation-table'],
  pathologicalDiagnosisTable: ['pathological-diagnosis-table'],
  icuTable: ['icu-table'],

  tcmDiagnosisTable: ['tcm-diagnosis-table'],

  'department-transfer-table': ['department-transfer-table'],

  departmentTransferTable: ['department-transfer-table'],
};

export const keyToReadonlyColumns = {
  'diagnosis-table': icdeColumns,
  'operation-table': operationColumns,
  'pathological-diagnosis-table': pathologyIcdeColumns,
  'icu-table': icuColumns,
  'tcm-diagnosis-table': tcmIcdeColumns,

  diagnosisTable: icdeColumns,
  operationTable: operationColumns,
  pathologicalDiagnosisTable: pathologyIcdeColumns,
  icuTable: icuColumns,
  tcmDiagnosisTable: tcmIcdeColumns,

  'department-transfer-table': departmentTransferColumns,
  departmentTransferTable: departmentTransferColumns,
};

const pickExcludeKeys = ['IcdeExtra', 'OperExtra'];

export const diffTableData = (
  oldTableDataItem: any,
  newTableDataItem: any,
  key: string,
) => {
  let diffResults = [];
  let pickKeys = (keyToReadonlyColumns?.[key] ?? [])
    ?.filter((item) => {
      return item?.visible === true && !item?.dataIndex?.includes('Extra');
    })
    ?.map((item) => {
      return item?.dataIndex;
    })
    ?.filter((item) => {
      return !pickExcludeKeys?.includes(item);
    });

  let oldTableData = (Object.values(oldTableDataItem)?.at(0) ?? []) as any[];
  let newTableData = (Object.values(newTableDataItem)?.at(0) ?? []) as any[];

  let oldTableDataFiltered = oldTableData
    ?.map((item, index) => {
      item['ItemSort'] = index + 1;
      return pick(item, pickKeys);
    })
    ?.sort(
      (a, b) =>
        (a?.ItemSort ?? Number.MAX_SAFE_INTEGER) -
        (b?.ItemSort ?? Number.MAX_SAFE_INTEGER),
    );

  let newTableDataFiltered = newTableData
    ?.map((item, index) => {
      item['ItemSort'] = index + 1;
      return pick(item, pickKeys);
    })
    ?.sort(
      (a, b) =>
        (a?.ItemSort ?? Number.MAX_SAFE_INTEGER) -
        (b?.ItemSort ?? Number.MAX_SAFE_INTEGER),
    );

  // 一样多
  // old 多 new 少
  // old 少 new 多
  let diffDataSize = oldTableDataFiltered?.length;

  let extraTableData = [];
  let sizeDiffer = undefined;
  if (oldTableDataFiltered?.length > newTableDataFiltered?.length) {
    diffDataSize = newTableDataFiltered?.length;
    extraTableData = oldTableDataFiltered?.slice(newTableDataFiltered?.length);
    sizeDiffer = 'DMR';
  } else if (oldTableDataFiltered?.length < newTableDataFiltered?.length) {
    extraTableData = newTableDataFiltered?.slice(oldTableDataFiltered?.length);
    sizeDiffer = 'READONLY';
  }

  for (let index = 0; index < diffDataSize; index++) {
    pickKeys?.forEach((itemKey) => {
      let itemDiffResults = Diff.diffLines(
        oldTableDataFiltered?.at(index)?.[itemKey]?.toString() ?? '',
        newTableDataFiltered?.at(index)?.[itemKey]?.toString() ?? '',
      );
      if (
        itemDiffResults?.filter((item) => item?.added || item?.removed)
          ?.length > 0
      ) {
        // 表示有 不同的地方
        diffResults.push({
          tableItemId: key,
          itemKey: itemKey,
          tableItemIndex: index,
          diffResults: itemDiffResults,
        });
      }
    });
  }

  // 额外的一个 diff 因为数据大小不同
  if (!isEmptyValues(extraTableData)) {
    extraTableData?.forEach((item, index) => {
      diffResults.push({
        tableItemId: key,
        itemKey: undefined,
        dataType: sizeDiffer,
        tableItemIndex: diffDataSize + index,
        diffResults: [
          {
            add: true,
            removed: false,
            count: 1,
            value: item,
          },
        ],
      });
    });
  }

  console.log(
    'DiffResults',
    oldTableDataFiltered,
    newTableDataFiltered,
    diffResults,
  );

  return diffResults;
};
