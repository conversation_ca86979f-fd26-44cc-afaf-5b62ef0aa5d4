import React, { useEffect } from 'react';
import { Modal, Form, message } from 'antd';
import { useRequest } from 'umi';
import dayjs from 'dayjs';
import ProFormContainer from '@uni/components/src/pro-form-container';
import { isRespErr } from '@/utils/widgets';
import { BulkChangeFormItems } from './formItems';
import './styles.less';
import { uniCommonService } from '@uni/services/src/index';

// 请求类型常量
export enum BedActionType {
  UpdateBedAmts = 'UpdateBedAmts',
}

/**
 * API响应类型
 */
interface RespVO<T = any> {
  code: number;
  statusCode: number;
  data?: T;
  message?: string;
  success?: boolean;
}

/**
 * 批量修改床位数Modal组件的Props接口
 */
export interface BedModificationModalProps {
  /** 是否显示modal */
  visible: boolean;
  /** 初始值 */
  initialValues?: Record<string, any>;
  /** 医院列表 */
  hospList: Array<{ label: string; value: string }>;
  /** 科室列表 */
  deptList: Array<{ label: string; value: string }>;
  /** API地址 */
  apiUrl: string;
  /** 取消回调 */
  onCancel: () => void;
  /** 成功回调 - 在API请求成功后调用 */
  onOk?: (values: Record<string, any>, apiResponse: any) => Promise<void>;
}

/**
 * 批量修改床位数Modal组件
 * 用于批量修改多个科室在指定日期范围内的床位数据
 */
const BedModificationModal: React.FC<BedModificationModalProps> = ({
  visible,
  initialValues,
  hospList,
  deptList,
  apiUrl,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();

  // 处理表单初始值
  useEffect(() => {
    if (visible && initialValues) {
      // 特殊处理DateRange字段，将Sdate和Edate转换为数组
      const formValues = {
        ...initialValues,
        DateRange:
          initialValues?.Sdate && initialValues?.Edate
            ? [initialValues.Sdate, initialValues.Edate]
            : undefined,
      };

      // 使用setFieldsValue设置表单值
      form.setFieldsValue(formValues);
    }
  }, [visible, initialValues, form]);

  // 调用接口更新床位数据
  const { run, loading } = useRequest(
    (data) =>
      uniCommonService(apiUrl, {
        method: 'POST',
        data,
        dataType: 'dyn-ddr',
        requestType: 'json',
      }),
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: async (res, params) => {
        if (!isRespErr(res)) {
          message.success('操作成功');
          // 如果有传入onOk回调，则调用
          // 关闭弹窗
          onCancel();
          if (onOk) {
            await onOk(params[0], res);
          }
        } else {
          // API请求失败，显示错误信息
          message.error(res?.message || '操作失败');
        }
      },
      onError: () => {
        message.error('操作失败，请稍后重试');
      },
    },
  );

  // 表单提交处理
  const handleFinish = async (values: Record<string, any>) => {
    try {
      await run(values);
    } catch (error) {
      console.error('提交表单错误:', error);
    }
  };

  return (
    <Modal
      open={visible}
      width={600}
      title="修改床位数据"
      destroyOnClose={true}
      onCancel={onCancel}
      confirmLoading={loading}
      onOk={() => form?.submit()}
      className="bed-modification-modal"
    >
      <ProFormContainer
        form={form}
        preserve={false}
        grid={true}
        labelCol={{}}
        wrapperCol={{}}
        searchOpts={BulkChangeFormItems(hospList, deptList)}
        initialValues={initialValues}
        onFinish={handleFinish}
      />
    </Modal>
  );
};

export default BedModificationModal;
