import {
  Card,
  Col,
  Row,
  Tabs,
  Slider,
  InputNumber,
  Input,
  Modal,
  Select,
} from 'antd';
import _ from 'lodash';
import { useEffect, useState, useRef } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import CustomGradientChart from '../customGradientChart/index';
import UniTable from '@uni/components/src/table';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import CardEchart from '@uni/components/src/cardEchart';
// import { FeeChargeDistributioByMedChrgitmLabelLinePieOption } from './echarts.opts';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import CardWithBtns from '@uni/components/src/cardWithBtns';
import SingleList from '../SingleList/index';
import { useDeepCompareEffect } from 'ahooks';
import { isEmptyValues } from '@uni/utils/src/utils';
import UniEcharts from '@uni/components/src/echarts/index';
import { BarCharts } from './charts.opts';
import { UniSelect } from '@uni/components/src/index';
import IconBtn from '@uni/components/src/iconBtn/index';

export interface ISingleColumnTabeProps {
  tableParams: any;
  args: {
    api: string;
    extraApiArgs?: any; // 额外入参
    columns?: any[]; // 本地columns
  };
  title?: string; // charts & 导出 title
  category?: string; // single columns table 要显示的column
  orderKey?: string;
  visibleValueKeys?: string | string[]; // value key
  dictData?: any; // 字典数据
  colSpan?: any;
  type?: 'table' | 'list'; // table | list 为list时 visibleValueKeys 只能是string 为table时 visibleValueKeys 是string[] 且category无效
  rowKey?: string; // table | list rowKey
  select?: {
    dataKey: string; // dictData 的 dataKey
    valueKey: string; // 匹配过滤 dataSource 用的key
    allowClear?: boolean; // 是否允许清除
    defaultSelect?: boolean; // 是否默认选择第一条
  };
  chart?: {
    api?: string;
    title?: string;
    options?: any; // options
    type?: string; // type 目前就bar一个
    valueKeys?: [string]; // 没有options才用
    category?: string; // 没有options才用
    yAxis?: string;
    colSpan?: any;
  }; // chart
  detailAction?: (record: any) => void;
}

const SingleColumnTable = ({
  tableParams,
  args,
  title,
  orderKey,
  visibleValueKeys,
  category,
  type,
  rowKey,
  select,
  dictData,
  colSpan,
  chart,
  detailAction,
}: ISingleColumnTabeProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [listColumns, setListColumns] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [selectedTableItem, setSelectedTableItem] = useState(undefined);

  const [selectedValue, setSelectedValue] = useState(undefined);
  // modal
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState([]);

  // Columns
  const {
    data: ColumnsData,
    loading: getColumnsLoading,
    mutate: mutateColumns,
    run: getColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(args?.api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor(args?.columns, res.data?.Columns);
        }
      },
    },
  );

  // 获取数据
  const {
    data: OriginalData,
    loading: getDataSourceLoading,
    run: getDataSourceReq,
  } = useRequest(
    (data) => {
      const deepData = _.cloneDeep(data);
      if (select?.defaultSelect) {
        // 选择了select
        if (selectedValue) {
          deepData[`${select?.valueKey}`] = selectedValue;
        } else {
          delete deepData[`${select?.valueKey}`];
        }
      }
      return uniCommonService(args?.api, {
        method: 'POST',
        data: deepData,
      });
    },
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 做容错 有些返回的用Stats包裹，有些则是直接用arr
          //  setDataSource(res?.data?.Stats ?? res?.data);
          // 还要判断
          //   if (
          //     args?.columns &&
          //     args?.columns?.findIndex((d) => d?.defaultSortOrder === 'descend') >
          //       -1
          //   ) {
          //     // 同时给一个值 因为排序了..
          //     setSelectedTableItem(
          //       _.maxBy(res?.data?.Stats ?? res?.data, 'PatCnt'),
          //     );
          //   } else {
          //     setSelectedTableItem(res.data.Stats?.at(0) ?? res?.data?.at(0));
          //   }
          // 目前没用到
          return res.data;
        }
      },
    },
  );

  useEffect(() => {
    if (args?.api && !ColumnsData) {
      getColumnsReq();
    }
  }, [args?.api, ColumnsData]);

  useEffect(() => {
    if (
      type === 'table' &&
      visibleValueKeys?.length > 0 &&
      ColumnsData?.length > 0
    ) {
      setListColumns([
        {
          dataIndex: 'options',
          width: 40,
          visible: true,
          fixed: 'left',
          render: (text, record) => (
            <IconBtn
              type="details"
              style={{ margin: '0 10px' }}
              onClick={(e) => {
                detailAction && detailAction(record);
              }}
            />
          ),
        },
        ...ColumnsData?.filter((col) =>
          visibleValueKeys?.includes(col?.dataIndex),
        )?.map((col) => ({ ...col, visible: true })),
      ]);
    } else if (
      type === 'table' &&
      !visibleValueKeys?.length &&
      ColumnsData?.length > 0
    ) {
      setListColumns([
        {
          dataIndex: 'options',
          width: 40,
          visible: true,
          fixed: 'left',
          render: (text, record) => (
            <IconBtn
              type="details"
              style={{ margin: '0 10px' }}
              onClick={(e) => {
                detailAction && detailAction(record);
              }}
            />
          ),
        },
        ...ColumnsData?.filter((col) => col?.visible),
      ]);
    }
  }, [type, visibleValueKeys, ColumnsData, detailAction]);

  // chart api
  const {
    data: ChartColumnsData,
    loading: getChartColumnsLoading,
    mutate: mutateChartColumns,
    run: getChartColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(chart?.api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );
  const {
    data: ChartData,
    loading: getChartApiLoading,
    run: getChartApiReq,
  } = useRequest(
    (data) =>
      uniCommonService(chart?.api, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
      },
    },
  );

  useEffect(() => {
    if (chart?.api && !ChartColumnsData) {
      getChartColumnsReq();
    }
  }, [chart?.api, ChartColumnsData]);

  useDeepCompareEffect(() => {
    if (!isEmptyValues(tableParams) && args?.api) {
      if (select?.defaultSelect) {
        if (selectedValue) {
          getDataSourceReq({
            ...tableParams,
            [`${select?.valueKey}`]: selectedValue,
            ...args?.extraApiArgs,
          });
        }
      } else {
        getDataSourceReq({ ...tableParams, ...args?.extraApiArgs });
      }
    }
    if (!isEmptyValues(tableParams) && chart?.api) {
      getChartApiReq({ ...tableParams, ...args?.extraApiArgs });
    }
  }, [tableParams, args, select?.defaultSelect, selectedValue, chart]);

  useEffect(() => {
    if (OriginalData?.length > 0) {
      setDataSource(
        selectedValue
          ? OriginalData?.filter(
              (data) =>
                data?.[select?.valueKey]?.toString().toUpperCase() ===
                selectedValue,
            )
          : OriginalData,
      );
    } else {
      setDataSource([]);
    }
  }, [selectedValue, OriginalData, select?.valueKey]);

  // 默认选择select
  useEffect(() => {
    if (select?.defaultSelect) {
      setSelectedValue(dictData?.[select?.dataKey]?.at(0)?.Code);
    }
  }, [select?.defaultSelect, dictData?.[select?.dataKey]]);

  return (
    <>
      <Col
        {...(colSpan
          ? colSpan
          : {
              xs: 24,
              sm: 24,
              md: 24,
              lg: 12,
              xl: 12,
            })}
      >
        <CardWithBtns
          content={
            type === 'table' ? (
              <UniTable
                loading={getDataSourceLoading}
                id={`matrix-table-${title}`}
                className={`settle-comp-stats-by-adrg-table`}
                rowKey={rowKey ? rowKey : 'id'}
                scroll={{
                  x: 'max-content',
                }}
                dictionaryData={dictData}
                columns={listColumns || []}
                dataSource={_.orderBy(
                  dataSource,
                  [orderKey || visibleValueKeys[1]],
                  ['desc'],
                )}
              />
            ) : (
              <SingleList
                loading={getDataSourceLoading}
                columns={ColumnsData}
                data={_.orderBy(dataSource, [visibleValueKeys], ['desc']).map(
                  (data, i) => {
                    return {
                      key: i + 1,
                      name: data?.[category],
                      value: data?.[visibleValueKeys as string],
                      ...data,
                    };
                  },
                )}
                listExtraAction={detailAction}
              ></SingleList>
            )
          }
          extra={
            select ? (
              <Select
                options={dictData?.[select?.dataKey]}
                style={{ width: '200px' }}
                fieldNames={{ value: 'Code', label: 'Name' }}
                placeholder="请选择"
                allowClear={select.allowClear ?? true}
                value={selectedValue}
                onChange={(value) => {
                  setSelectedValue(value);
                }}
              />
            ) : undefined
          }
          needExport={true}
          exportTitle={title}
          exportData={dataSource}
          exportColumns={ColumnsData}
          needModalDetails={true}
          onRefresh={() => {
            getDataSourceReq({ ...tableParams, ...args?.extraApiArgs });
          }}
          columnsEditableUrl={args?.api}
          onColumnChange={(newColumns) => {
            mutateColumns(tableColumnBaseProcessor(args?.columns, newColumns));
          }}
        />
      </Col>
      {chart && (
        <Col
          {...(chart?.colSpan
            ? chart?.colSpan
            : {
                xs: 24,
                sm: 24,
                md: 24,
                lg: 12,
                xl: 12,
              })}
        >
          <CardWithBtns
            title={chart?.title || '疑难病例病种结构分布'}
            content={
              <UniEcharts
                height={250}
                elementId="charts"
                loading={getDataSourceLoading}
                options={
                  chart?.options
                    ? chart?.options(ChartData)
                    : chart?.type === 'bar'
                    ? BarCharts(
                        ChartData,
                        chart.category,
                        chart.valueKeys,
                        chart?.yAxis,
                      )
                    : {}
                }
              />
            }
            needExport={true}
            exportTitle={title}
            exportData={ChartData}
            exportColumns={ChartColumnsData}
            needModalDetails={true}
            onRefresh={() => {
              getChartApiReq({ ...tableParams, ...args?.extraApiArgs });
            }}
            columnsEditableUrl={chart?.api}
            onColumnChange={(newColumns) => {
              mutateChartColumns(tableColumnBaseProcessor([], newColumns));
            }}
          />
        </Col>
      )}

      <Modal
        title="详情"
        open={visible}
        onCancel={() => {
          setVisible(false);
          setRecord(undefined);
        }}
        width={800}
        okButtonProps={{ style: { display: 'none' } }}
        footer={null}
      >
        <UniTable
          rowKey="key"
          id="single-detail-table"
          columns={ColumnsData}
          dataSource={record ? [record] : []}
          scroll={{ x: 'max-content' }}
        />
      </Modal>
    </>
  );
};

export default SingleColumnTable;
