let db;

// 初始化IndexedDB
const initDB = async () => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('DraftDatabase', 1);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      db = request.result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('drafts')) {
        const store = db.createObjectStore('drafts', {
          keyPath: 'id',
          autoIncrement: true,
        });
        store.createIndex('hisId', 'hisId', { unique: false });
      }
    };
  });
};

// 保存草稿
const saveDraft = async (hisId, content) => {
  const transaction = db.transaction(['drafts'], 'readwrite');
  const store = transaction.objectStore('drafts');

  // 先删除同一hisId的旧记录
  const index = store.index('hisId');
  const request = index.getAll(hisId);

  return new Promise((resolve, reject) => {
    request.onsuccess = () => {
      const existingRecords = request.result;

      // 删除旧记录
      existingRecords.forEach((record) => {
        store.delete(record.id);
      });

      // 添加新记录
      const draftItem = {
        hisId,
        content,
        timestamp: Date.now(),
      };

      const addRequest = store.add(draftItem);
      addRequest.onsuccess = () => resolve(draftItem);
      addRequest.onerror = () => reject(addRequest.error);
    };

    request.onerror = () => reject(request.error);
  });
};

// 获取草稿
const getDraft = async (hisId) => {
  const transaction = db.transaction(['drafts'], 'readonly');
  const store = transaction.objectStore('drafts');
  const index = store.index('hisId');

  return new Promise((resolve, reject) => {
    const request = index.get(hisId);
    request.onsuccess = () => resolve(request.result || {});
    request.onerror = () => reject(request.error);
  });
};

// 删除草稿
const deleteDraft = async (hisId) => {
  const transaction = db.transaction(['drafts'], 'readwrite');
  const store = transaction.objectStore('drafts');
  const index = store.index('hisId');

  return new Promise((resolve, reject) => {
    const request = index.getAll(hisId);
    request.onsuccess = () => {
      const records = request.result;
      records.forEach((record) => {
        store.delete(record.id);
      });
      resolve(true);
    };
    request.onerror = () => reject(request.error);
  });
};

// 验证草稿
const validateDraft = async (hisId, dmrLatestDate) => {
  const draftItem = await getDraft(hisId);

  if (!draftItem || !draftItem.content || !draftItem.timestamp) {
    await deleteDraft(hisId);
    return false;
  }

  const latestTimestamp = new Date(dmrLatestDate).getTime();
  if (latestTimestamp >= draftItem.timestamp) {
    await deleteDraft(hisId);
    return false;
  }

  return true;
};

// 初始化数据库
initDB()
  .then(() => {
    self.postMessage({ type: 'READY' });
  })
  .catch((error) => {
    self.postMessage({ type: 'ERROR', error: error.message });
  });

// 处理主线程消息
self.onmessage = async (event) => {
  const { type, payload } = event.data;

  try {
    let result;

    switch (type) {
      case 'SAVE_DRAFT':
        result = await saveDraft(payload.hisId, payload.content);
        break;
      case 'GET_DRAFT':
        result = await getDraft(payload.hisId);
        break;
      case 'DELETE_DRAFT':
        result = await deleteDraft(payload.hisId);
        break;
      case 'VALIDATE_DRAFT':
        result = await validateDraft(payload.hisId, payload.dmrLatestDate);
        break;
      default:
        throw new Error('Unknown message type');
    }

    self.postMessage({ type: 'SUCCESS', data: result });
  } catch (error) {
    self.postMessage({ type: 'ERROR', error: error.message });
  }
};
