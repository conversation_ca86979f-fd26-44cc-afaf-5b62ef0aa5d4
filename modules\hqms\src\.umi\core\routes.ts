// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+runtime@3.5.34_react@16.14.0/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@uni/components/src/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "exact": true,
    "redirect": "/oper/hosp"
  },
  {
    "path": "/oper",
    "routes": [
      {
        "path": "/oper/hosp",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__oper__hosp__index2' */'@/pages/oper/hosp/index2'), loading: LoadingComponent})
      },
      {
        "path": "/oper/dept",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__oper__dept__index2' */'@/pages/oper/dept/index2'), loading: LoadingComponent})
      },
      {
        "path": "/oper/medTeam",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__oper__medTeam__index2' */'@/pages/oper/medTeam/index2'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/cmi",
    "routes": [
      {
        "path": "/cmi/hospLevelCmi",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__cmi__hospLevelCmi__index' */'@/pages/cmi/hospLevelCmi/index'), loading: LoadingComponent})
      },
      {
        "path": "/cmi/deptLevelCmi",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__cmi__deptLevelCmi__index' */'@/pages/cmi/deptLevelCmi/index'), loading: LoadingComponent})
      },
      {
        "path": "/cmi/medTeamLevelCmi",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__cmi__medTeamLevelCmi__index' */'@/pages/cmi/medTeamLevelCmi/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/quality",
    "routes": [
      {
        "path": "/quality/hosp",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__quality__hosp__index2' */'@/pages/quality/hosp/index2'), loading: LoadingComponent})
      },
      {
        "path": "/quality/dept",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__quality__dept__index2' */'@/pages/quality/dept/index2'), loading: LoadingComponent})
      },
      {
        "path": "/quality/medTeam",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__quality__medTeam__index2' */'@/pages/quality/medTeam/index2'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/operComplicationComposition",
    "routes": [
      {
        "path": "/operComplicationComposition/hosp",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__operComplicationComposition__hosp__index2' */'@/pages/operComplicationComposition/hosp/index2'), loading: LoadingComponent})
      },
      {
        "path": "/operComplicationComposition/dept",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__operComplicationComposition__dept__index2' */'@/pages/operComplicationComposition/dept/index2'), loading: LoadingComponent})
      },
      {
        "path": "/operComplicationComposition/medTeam",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__operComplicationComposition__medTeam__index2' */'@/pages/operComplicationComposition/medTeam/index2'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/sd",
    "routes": [
      {
        "path": "/sd/hosp",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__sd__hosp__index2' */'@/pages/sd/hosp/index2'), loading: LoadingComponent})
      },
      {
        "path": "/sd/dept",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__sd__dept__index2' */'@/pages/sd/dept/index2'), loading: LoadingComponent})
      },
      {
        "path": "/sd/medTeam",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__sd__medTeam__index2' */'@/pages/sd/medTeam/index2'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/tcm",
    "routes": [
      {
        "path": "/tcm/hosp",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__tcm__hosp__index2' */'@/pages/tcm/hosp/index2'), loading: LoadingComponent})
      },
      {
        "path": "/tcm/dept",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__tcm__dept__index2' */'@/pages/tcm/dept/index2'), loading: LoadingComponent})
      },
      {
        "path": "/tcm/medTeam",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__tcm__medTeam__index2' */'@/pages/tcm/medTeam/index2'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/score",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__quality__score__index2' */'@/pages/quality/score/index2'), loading: LoadingComponent})
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
