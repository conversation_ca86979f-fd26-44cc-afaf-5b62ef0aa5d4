import { Emitter } from '@uni/utils/src/emitter';
import React from 'react';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { Tag, Space } from 'antd';
import IconBtn from '@uni/components/src/iconBtn';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { useModel } from 'umi';

export const usersColumns = (hospList) => [
  {
    dataIndex: 'Id',
    title: 'Id',
    align: 'center',
  },
  {
    dataIndex: 'UserName',
    title: '用户名',
    width: 150,
    visible: true,
    align: 'center',
  },
  {
    dataIndex: 'EmployeeCode',
    title: '工号',
    width: 150,
    visible: true,
    align: 'center',
    render: (node, record, index) => {
      return (
        <span>{`${record?.['EmployeeCode'] || '-'}：${
          record?.['EmployeeName'] || '-'
        }`}</span>
      );
    },
  },
  {
    dataIndex: 'Roles',
    title: '用户组',
    visible: true,
    align: 'center',
    render: (node, record, index) => {
      return (
        <>
          {record['Roles'].length > 0 ? (
            record['Roles'].split(',').map((name) => {
              return (
                <Tag
                  color="geekblue"
                  style={{
                    margin: '5px 5px',
                    borderColor: 'transparent',
                    borderRadius: '4px',
                  }}
                >
                  {name}
                </Tag>
              );
            })
          ) : (
            <></>
          )}
        </>
      );
    },
  },
  {
    dataIndex: 'HospCode',
    title: '用户所属院区',
    visible: true,
    align: 'center',
    render: (node, record, index) => {
      return (
        <>
          {record['HospCode'].length > 0 ? (
            record['HospCode'].map((name) => {
              return (
                <Tag
                  style={{
                    margin: '5px 5px',
                    borderColor: 'transparent',
                    borderRadius: '4px',
                  }}
                >
                  {hospList?.find((item) => item?.Code === name)?.Name || name}
                </Tag>
              );
            })
          ) : (
            <></>
          )}
        </>
      );
    },
  },
  {
    dataIndex: 'IsLocked',
    title: '用户是否被锁定',
    visible: true,
    width: 75,
    align: 'center',
    render: (node, record, index) => {
      return (
        <>
          {record['IsLocked'] ? (
            <span className="font-danger">'已锁定'</span>
          ) : (
            '未锁定'
          )}
        </>
      );
    },
  },
  {
    title: '操作',
    visible: true,
    align: 'center',
    width: 150,
    render: (node, record, index) => {
      return (
        <div className={'configuration-item-operation'}>
          <Space>
            <IconBtn
              type="edit"
              title="修改"
              onClick={() => {
                Emitter.emit(ConfigurationEvents.USERS_SUBSYSTEM_EDIT, record);
              }}
            />
            <IconBtn
              type="key"
              title="修改用户密码"
              onClick={() => {
                Emitter.emit(
                  ConfigurationEvents.USERS_SUBSYSTEM_EDIT_PWD,
                  record,
                );
              }}
            />
            <IconBtn
              type="undo"
              title="重置用户密码"
              openPop={true}
              popOnConfirm={() => {
                Emitter.emit(
                  ConfigurationEvents.USERS_SUBSYSTEM_RESET_PWD,
                  record,
                );
              }}
            />
            <IconBtn
              type="unlock"
              openPop={true}
              popOnConfirm={() => {
                Emitter.emit(
                  ConfigurationEvents.USERS_SUBSYSTEM_UNLOCK,
                  record,
                );
              }}
            />
            <IconBtn
              type="delete"
              openPop={true}
              popOnConfirm={() => {
                Emitter.emit(
                  ConfigurationEvents.USERS_SUBSYSTEM_DELETE,
                  record,
                );
              }}
            />
          </Space>
        </div>
      );
    },
  },
];
