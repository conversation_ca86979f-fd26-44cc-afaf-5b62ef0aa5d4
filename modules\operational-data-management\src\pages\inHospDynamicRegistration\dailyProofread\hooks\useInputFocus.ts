import { useCallback, useEffect } from 'react';
import { useSafeState } from 'ahooks';

/**
 * 输入框聚焦hook
 */
export const useInputFocus = () => {
  // 全局配置
  const autoSkipFocusKeys =
    (window as any).externalConfig?.['operationalDataManagement']
      ?.skipFocusKeys ?? [];

  const [nowAllInput, setNowAllInput] = useSafeState({
    inputs: [],
    clkTarget: undefined,
  });

  const getAllInputs = useCallback(
    (target = null, type = 'update') => {
      setTimeout(() => {
        let allInputs = document
          .querySelector('#dailyProofreadTable')
          ?.querySelectorAll('input');
        if (!allInputs?.length) {
          setNowAllInput({
            inputs: [],
            clkTarget: undefined,
          });
        } else {
          if (type === 'create') {
            setNowAllInput({
              inputs: Array.from(allInputs),
              clkTarget: target,
            });
          } else {
            setNowAllInput({
              inputs: Array.from(allInputs)?.filter(
                (input) => input.disabled === false,
              ) as any,
              clkTarget: target,
            });
          }
        }
      }, 0);
    },
    [setNowAllInput],
  );

  useEffect(() => {
    if (nowAllInput?.inputs?.length) {
      for (let i = 0; i < nowAllInput?.inputs?.length; i++) {
        const element = nowAllInput?.inputs?.[i];
        if (nowAllInput.clkTarget) {
          if (
            element?.id?.split('_')?.at(1) ===
            (nowAllInput.clkTarget?.dataIndex || nowAllInput.clkTarget?.data)
          ) {
            element.focus();
            break;
          }
        } else {
          if (
            !element?.disabled &&
            autoSkipFocusKeys?.findIndex(
              (key) => key === element?.getAttribute('data-index'),
            ) === -1
          ) {
            element.focus();
            break;
          }
        }
      }
    }
  }, [nowAllInput]);

  return { nowAllInput, getAllInputs };
};
