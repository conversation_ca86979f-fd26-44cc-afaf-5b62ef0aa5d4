import { Form } from "antd";
import { useMemo, useRef } from "react";
import { useDraft } from "./useDraft";

const useDraftForm = (hisId: string) => {
  const [form] = Form.useForm();
  const { getDraft, saveDraft } = useDraft({ hisId });
  const prevDataRef = useRef('');
  // feat: 保证draft唯一 - 跟踪hisId变化
  const prevHisIdRef = useRef('');

  const debouncedSave = useMemo(
    () => saveDraft,
    [saveDraft]
  );

  const FormMonitor = useMemo(() => {
    return () => (
      <Form.Item shouldUpdate noStyle>
        {() => {
          // feat: 保证draft唯一 - 当hisId变化时重置prevDataRef
          if (prevHisIdRef.current !== hisId) {
            prevDataRef.current = '';
            prevHisIdRef.current = hisId;
          }

          const data = form.getFieldsValue(true);
          const dataStr = JSON.stringify(data || '');

          if (dataStr !== prevDataRef.current) {
            console.log('data changed', data);
            prevDataRef.current = dataStr;
            debouncedSave(data);
          }

          return null;
        }}
      </Form.Item>
    );
  }, [debouncedSave, hisId]);

  return { form, FormMonitor, getDraft };
};

export default useDraftForm