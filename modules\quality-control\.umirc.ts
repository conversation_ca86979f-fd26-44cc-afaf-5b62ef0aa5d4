import { name } from './package.json';
import {
  slaveCommonConfig,
  extraBabelIncludes,
  extraWebPackPlugin,
  title,
} from '../../.umirc.commom';

export default {
  // title: title,
  base: name,
  publicPath: '/qualityControl/',
  outputPath: '../../dist/qualityControl',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  qiankun: {
    master: {
      // 注册子应用信息
      apps: [
        {
          name: 'energy-efficiency-management',
          entry:
            process.env.NODE_ENV === 'production'
              ? '/energyEfficiencyManagement/'
              : '//localhost:8951',
        },
      ],
    },
    slave: {},
  },

  plugins: [
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/main/analysis/hosp',
    },
    {
      path: '/main/analysis/hosp',
      exact: true,
      component: '@/pages/analysis/hosp2',
    },
    // {
    //   path: '/main/analysis/dept',
    //   exact: true,
    //   component: '@/pages/analysis/dept',
    // },
    {
      path: '/main/analysis/match',
      exact: true,
      component: '@/pages/analysis/match',
    },
    {
      path: '/main/rule',
      exact: true,
      component: '@/pages/rule/index',
    },
    // 自定义规则配置
    {
      path: '/main/customRule',
      exact: true,
      component: '@/pages/customRule/index',
    },
    {
      path: '/main/details/type',
      exact: true,
      component: '@/pages/details/type',
    },
    {
      path: '/main/details/casebook',
      exact: true,
      component: '@/pages/details/casebook',
    },

    {
      path: '/main/efficiency',
      exact: true,
      redirect: '/main/efficiency/codeValueQuantification',
    },
    {
      path: '/main/efficiency/codeValueQuantification',
      exact: true,
      component: '@/pages/energyEfficiency/codeValueQuantification/index',
    },
    {
      path: '/main/efficiency/codeWorkerLoad',
      exact: true,
      component: '@/pages/energyEfficiency/codeWorkerLoad/index',
    },

    {
      path: '/doctor',
      exact: true,
      redirect: '/doctor/analysis/hosp',
    },
    {
      path: '/doctor/analysis/hosp',
      exact: true,
      component: '@/pages/doctor/analysis/hosp2',
    },
    {
      path: '/doctor/analysis/dept',
      exact: true,
      component: '@/pages/doctor/analysis/dept2',
    },
    {
      path: '/doctor/details',
      exact: true,
      redirect: '/doctor/details/casebook',
    },
    {
      path: '/doctor/details/casebook',
      exact: true,
      component: '@/pages/doctor/details/casebook',
    },
    {
      path: '/doctor/details/type',
      exact: true,
      component: '@/pages/doctor/details/type',
    },
  ],

  proxy: {
    // 同cra的setupProxy,代理中转实现dev版本的跨域
    '/common': {
      target: 'http://172.16.3.111:9200',
      changeOrigin: true,
      pathRewrite: { '^/common': '' },
      secure: false, // https的dev后端的话需要配
    },

    '/metaData': {
      target: 'http://172.16.3.111:9200',
      changeOrigin: true,
      pathRewrite: { '^/metaData': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
