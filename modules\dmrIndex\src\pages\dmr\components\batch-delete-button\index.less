// 根据实际DOM结构，Popconfirm会包装一层span，所以需要匹配正确的结构

// 通用样式 - 匹配所有span
.ant-btn span {
  &.batch-delete-text {
    color: rgba(0, 0, 0, 0.88) !important;
    font-weight: normal !important;
    font-size: 14px !important;
  }
}

// Button组件的基础样式
.batch-delete-button {
  &.ant-btn {
    // 重置table title中的样式覆盖
    background: #ffffff !important;
    border: 1px solid #d9d9d9 !important;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02) !important;
    
    // 所有span都应用基础样式
    span {
      color: rgba(0, 0, 0, 0.88) !important;
      
      &.batch-delete-text {
        font-weight: normal !important;
        font-size: 14px !important;
      }
    }

    // hover状态
    &:hover:not(:disabled) {
      border-color: #4096ff !important;
      background: #ffffff !important;
      
      span {
        color: #4096ff !important;
      }
    }

    // disabled状态
    &:disabled {
      background: #ffffff !important;
      border-color: #d9d9d9 !important;
      cursor: not-allowed !important;
      box-shadow: none !important;
      
      span {
        color: rgba(0, 0, 0, 0.25) !important;
      }
    }

    // danger状态下的样式
    &.ant-btn-dangerous {
      &:hover:not(:disabled) {
        span {
          color: #ff4d4f !important;
        }
        
        border-color: #ff4d4f !important;
      }
    }
  }
}

// 全局强制样式，针对Popconfirm包装的情况
.ant-popover-disabled-compatible-wrapper .ant-btn span {
  color: rgba(0, 0, 0, 0.25) !important;
}

.ant-popover-disabled-compatible-wrapper .ant-btn span.batch-delete-text {
  color: rgba(0, 0, 0, 0.25) !important;
  font-weight: normal !important;
  font-size: 14px !important;
}

// 确保hover状态正确
button.ant-btn:hover:not(:disabled) span.batch-delete-text {
  color: #4096ff !important;
}

button.ant-btn.ant-btn-dangerous:hover:not(:disabled) span.batch-delete-text {
  color: #ff4d4f !important;
}

// 最强优先级，确保样式生效
button.ant-btn span.batch-delete-text {
  color: rgba(0, 0, 0, 0.88) !important;
  font-weight: normal !important;
  font-size: 14px !important;
}

button.ant-btn:disabled span.batch-delete-text {
  color: rgba(0, 0, 0, 0.25) !important;
}