// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+runtime@3.5.34_react@16.14.0/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@uni/components/src/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "exact": true,
    "redirect": "/main/index"
  },
  {
    "path": "/main/index",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__chs__index' */'@/pages/chs/index'), loading: LoadingComponent})
  },
  {
    "path": "/main/index/fullscreen",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__chs__index' */'@/pages/chs/index'), loading: LoadingComponent})
  },
  {
    "path": "/main/management",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__index' */'@/pages/management/index'), loading: LoadingComponent})
  },
  {
    "path": "/main/monitor",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__monitor__index' */'@/pages/monitor/index'), loading: LoadingComponent})
  },
  {
    "path": "/main/settleInfo",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__settleInfo__index' */'@/pages/settleInfo/index'), loading: LoadingComponent})
  },
  {
    "path": "/main/match",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__match' */'@/pages/match'), loading: LoadingComponent})
  },
  {
    "path": "/main/qcAnalysis/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__quality-check__hosp' */'@/pages/quality-check/hosp'), loading: LoadingComponent})
  },
  {
    "path": "/main/qcAnalysis/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__quality-check__dept' */'@/pages/quality-check/dept'), loading: LoadingComponent})
  },
  {
    "path": "/main/details/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__details__dept' */'@/pages/details/dept'), loading: LoadingComponent})
  },
  {
    "path": "/main/encode/diff",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__encodeAnalysis__diff' */'@/pages/encodeAnalysis/diff'), loading: LoadingComponent})
  },
  {
    "path": "/main/encode/details",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__encodeAnalysis__details' */'@/pages/encodeAnalysis/details'), loading: LoadingComponent})
  },
  {
    "path": "/main/specialDisease/discussion",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__specialDiseaseDiscussion' */'@/pages/specialDiseaseDiscussion'), loading: LoadingComponent})
  },
  {
    "path": "/main/specialDisease/reportStats",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__specialDiseaseDiscussion__pages__amountStatistics' */'@/pages/specialDiseaseDiscussion/pages/amountStatistics'), loading: LoadingComponent})
  },
  {
    "path": "/main/specialDisease/importRecord",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__specialDiseaseDiscussion__pages__importRecord' */'@/pages/specialDiseaseDiscussion/pages/importRecord'), loading: LoadingComponent})
  },
  {
    "path": "/dip/analysis/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dip__hosp' */'@/pages/dip/hosp'), loading: LoadingComponent})
  },
  {
    "path": "/dip/analysis/majorPerfDept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dip__majorPerfDept' */'@/pages/dip/majorPerfDept'), loading: LoadingComponent})
  },
  {
    "path": "/dip/analysis/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dip__dept' */'@/pages/dip/dept'), loading: LoadingComponent})
  },
  {
    "path": "/dip/analysis/group",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dip__group' */'@/pages/dip/group'), loading: LoadingComponent})
  },
  {
    "path": "/dip/analysis/medTeam",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dip__medTeam' */'@/pages/dip/medTeam'), loading: LoadingComponent})
  },
  {
    "path": "/dip/analysis/pay",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dip__pay' */'@/pages/dip/pay'), loading: LoadingComponent})
  },
  {
    "path": "/dip/exportDetail",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dip__exportDetail' */'@/pages/dip/exportDetail'), loading: LoadingComponent})
  },
  {
    "path": "/dip/warning/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__in__hosp' */'@/pages/warning-monitor/in/hosp'), loading: LoadingComponent})
  },
  {
    "path": "/dip/warning/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__in__dept' */'@/pages/warning-monitor/in/dept'), loading: LoadingComponent})
  },
  {
    "path": "/dip/warning/patientInfo",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__patientInfo' */'@/pages/warning-monitor/patientInfo'), loading: LoadingComponent})
  },
  {
    "path": "/dip/important/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__after__hosp' */'@/pages/warning-monitor/after/hosp'), loading: LoadingComponent})
  },
  {
    "path": "/dip/important/majorPerfDept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__after__majorPerfDept' */'@/pages/warning-monitor/after/majorPerfDept'), loading: LoadingComponent})
  },
  {
    "path": "/dip/important/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__after__dept' */'@/pages/warning-monitor/after/dept'), loading: LoadingComponent})
  },
  {
    "path": "/dip/important/medTeam",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__after__medTeam' */'@/pages/warning-monitor/after/medTeam'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/pre/index",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__analysis__index' */'@/pages/analysis/index'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/pre/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__analysis__hosp__index' */'@/pages/analysis/hosp/index'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/pre/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__analysis__dept__index' */'@/pages/analysis/dept/index'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/pre/disease",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__analysis__disease__index' */'@/pages/analysis/disease/index'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/drg/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__drg__hosp' */'@/pages/drg/hosp'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/drg/majorPerfDept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__drg__majorPerfDept' */'@/pages/drg/majorPerfDept'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/drg/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__drg__dept' */'@/pages/drg/dept'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/drg/medTeam",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__drg__medTeam' */'@/pages/drg/medTeam'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/drg/group",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__drg__group' */'@/pages/drg/group'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/drg/pay",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__drg__pay' */'@/pages/drg/pay'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/payDetail",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__drg__payDetail' */'@/pages/drg/payDetail'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/warning/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__in__hosp' */'@/pages/warning-monitor/in/hosp'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/warning/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__in__dept' */'@/pages/warning-monitor/in/dept'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/warning/patientInfo",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__patientInfo' */'@/pages/warning-monitor/patientInfo'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/important/hosp",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__after__hosp' */'@/pages/warning-monitor/after/hosp'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/important/majorPerfDept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__after__majorPerfDept' */'@/pages/warning-monitor/after/majorPerfDept'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/important/dept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__after__dept' */'@/pages/warning-monitor/after/dept'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/important/medTeam",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__after__medTeam' */'@/pages/warning-monitor/after/medTeam'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/important/variableConditions",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__variableConditions' */'@/pages/variableConditions'), loading: LoadingComponent})
  },
  {
    "path": "/dip/important/variableConditions",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dip__variableConditions' */'@/pages/dip/variableConditions'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/cardInfo",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__warning-monitor__components__cardInfo' */'@/pages/warning-monitor/components/cardInfo'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/payRuleSettings",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__settleCheck__payRuleSettings' */'@/pages/settleCheck/payRuleSettings'), loading: LoadingComponent})
  },
  {
    "path": "/dip/payRuleSettings",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__settleCheck__payRuleSettings' */'@/pages/settleCheck/payRuleSettings'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/report",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__normalReport' */'@/pages/report/normalReport'), loading: LoadingComponent})
  },
  {
    "path": "/analysis/highlight/:id",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__highlightReport' */'@/pages/report/highlightReport'), loading: LoadingComponent})
  },
  {
    "path": "/dip/report",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__normalReport' */'@/pages/report/normalReport'), loading: LoadingComponent})
  },
  {
    "path": "/dip/highlight/:id",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__highlightReport' */'@/pages/report/highlightReport'), loading: LoadingComponent})
  },
  {
    "path": "/main/report",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__normalReport' */'@/pages/report/normalReport'), loading: LoadingComponent})
  },
  {
    "path": "/main/highlight/:id",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__highlightReport' */'@/pages/report/highlightReport'), loading: LoadingComponent})
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
