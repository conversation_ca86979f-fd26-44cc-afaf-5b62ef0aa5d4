import { useEffect, useRef } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { findUnlockedRecordHandler } from '@/utils/widgets';
import { shortcuts } from '@/Constants';
import { TableAction } from '@uni/reducers/src/tableReducer';

/**
 * 键盘快捷键处理hook
 */
export const useKeyboardShortcuts = ({
  editableTableFormRef,
  editableTableActionRef,
  resetAutoCompute,
  EditableState,
  EditableDispatch,
  sortedFilteredTable,
  getAllInputs,
  nowAllInput,
  editSaveHandler,
}) => {
  // 创建一个ref来跟踪最新的nowAllInput值
  const nowAllInputRef = useRef(nowAllInput);

  // 当nowAllInput变化时更新ref
  useEffect(() => {
    nowAllInputRef.current = nowAllInput;
  }, [nowAllInput]);

  // 注意：useHotkeys 被移除，由外部调用

  useEffect(() => {
    // 保存快捷键处理
    const handleEditableSave = () => {
      if (EditableState.editableKeys.length > 0) {
        resetAutoCompute();
        let rowKey = Object.keys(
          editableTableFormRef.current?.getFieldsValue(),
        )?.at(0);
        let data = editableTableFormRef.current?.getFieldsValue()?.[rowKey];

        if (!rowKey?.includes('new')) {
          EditableDispatch({
            type: TableAction.editableKeysChange,
            payload: {
              editableKeys: [],
            },
          });
        }

        editSaveHandler(rowKey, data);
      }
    };

    // 取消快捷键处理
    const handleEditableCancel = () => {
      if (EditableState.editableKeys.length > 0) {
        resetAutoCompute();
        editableTableFormRef.current?.resetFields();
        editableTableActionRef.current?.cancelEditable(
          Object.keys(editableTableFormRef.current?.getFieldsValue())
            ?.at(0)
            ?.includes('new')
            ? Object.keys(editableTableFormRef.current?.getFieldsValue())?.at(0)
            : parseInt(
                Object.keys(editableTableFormRef.current?.getFieldsValue())?.at(
                  0,
                ),
              ),
        );
      }
    };

    // 自定义保存处理
    const handleCustomSave = (record: {
      targetKey: string;
      dataIndex: string;
    }) => {
      if (EditableState.editableKeys.length > 0) {
        resetAutoCompute();
        let rowKey = Object.keys(
          editableTableFormRef.current?.getFieldsValue(),
        )?.at(0);
        let data = editableTableFormRef.current?.getFieldsValue()?.[rowKey];

        if (!rowKey) return;

        if (!rowKey?.includes('new')) {
          if (record?.targetKey) {
            EditableDispatch({
              type: TableAction.editableKeysChange,
              payload: {
                editableKeys: [record?.targetKey],
              },
            });
          } else {
            // 自动跳到下一行
            let rowIndex = sortedFilteredTable.current.findIndex(
              (d) => d.Id?.toString() === rowKey?.toString(),
            );
            let findEditOrEnd = false;
            while (!findEditOrEnd) {
              if (
                rowIndex !== -1 &&
                rowIndex < sortedFilteredTable.current.length - 1
              ) {
                if (sortedFilteredTable.current?.[rowIndex + 1]?.IsLocked) {
                  rowIndex++;
                } else {
                  findEditOrEnd = true;
                  EditableDispatch({
                    type: TableAction.editableKeysChange,
                    payload: {
                      editableKeys: [
                        sortedFilteredTable.current?.[rowIndex + 1].Id,
                      ],
                    },
                  });
                }
              } else {
                findEditOrEnd = true;
                EditableDispatch({
                  type: TableAction.editableKeysChange,
                  payload: {
                    editableKeys: [],
                  },
                });
              }
            }
          }
        }

        getAllInputs(
          record?.dataIndex ? { dataIndex: record?.dataIndex } : undefined,
        );

        // 传入skipLoading=true参数，当通过handleCustomSave调用editSaveHandler时不显示loading
        editSaveHandler(rowKey, data, false, true);
      }
    };

    // 上下键导航处理
    const handleUpNavigation = () => {
      if (EditableState.editableKeys.length > 0) {
        let rowKey = Object.keys(
          editableTableFormRef.current?.getFieldsValue(),
        )?.at(0);
        let rowIndex = sortedFilteredTable.current?.findIndex(
          (item) => item.Id?.toString() === rowKey,
        );

        if (rowIndex <= 0) return;

        let firstNotIsLockedIndex = findUnlockedRecordHandler(
          sortedFilteredTable.current,
          rowIndex,
          'UP',
        );
        if (firstNotIsLockedIndex === -1) return;

        let dataIndex = document.activeElement.getAttribute('data-index');

        Emitter.emit(EventConstant.ODM_CUSTOM_TABLE_SAVE, {
          targetKey: sortedFilteredTable.current?.[firstNotIsLockedIndex]?.Id,
          dataIndex,
        });
      }
    };

    const handleDownNavigation = () => {
      if (EditableState.editableKeys.length > 0) {
        let rowKey = Object.keys(
          editableTableFormRef.current?.getFieldsValue(),
        )?.at(0);
        let rowIndex = sortedFilteredTable.current?.findIndex(
          (item) => item.Id?.toString() === rowKey,
        );

        if (
          rowIndex === -1 ||
          rowIndex >= sortedFilteredTable.current.length - 1
        )
          return;

        let firstNotIsLockedIndex = findUnlockedRecordHandler(
          sortedFilteredTable.current,
          rowIndex,
          'DOWN',
        );
        if (firstNotIsLockedIndex === -1) return;

        let dataIndex = document.activeElement.getAttribute('data-index');

        Emitter.emit(EventConstant.ODM_CUSTOM_TABLE_SAVE, {
          targetKey: sortedFilteredTable.current?.[firstNotIsLockedIndex]?.Id,
          dataIndex,
        });
      }
    };

    // 左右键导航
    const handleLeftRightNavigation = (event) => {
      if (EditableState.editableKeys.length > 0) {
        let index = -1;
        // 使用ref.current获取最新的nowAllInput值
        let { inputs } = nowAllInputRef.current;
        for (let i = 0; i < inputs?.length; i++) {
          if (inputs[i] === document.activeElement) index = i;
        }

        // 左移
        if (
          event.key === 'ArrowLeft' ||
          (event.shiftKey && event.key === 'Tab')
        ) {
          if (index !== inputs.length - 1 && index > 0) {
            inputs[index - 1].focus();
          }
        }
        // 右移
        else {
          if (index !== -1 && index < inputs.length - 1) {
            inputs[index + 1].focus();
          } else if (index === inputs.length - 1) {
            Emitter.emit(EventConstant.ODM_CUSTOM_TABLE_SAVE);
          }
        }
      }
    };

    // 页面离开提示
    const handleBeforeUnload = (event) => {
      if (EditableState?.editableKeys?.length > 0) {
        event.preventDefault();
        event.returnValue = '你确定要离开此页面？ 系统可能不会保存您所做的更改';
      }
    };

    // 注册监听器
    Emitter.on(EventConstant.EDITABLE_SAVE_SHORTCUT, handleEditableSave);
    Emitter.on(EventConstant.EDITABLE_CANCEL_SHORTCUT, handleEditableCancel);
    Emitter.on(EventConstant.ODM_CUSTOM_TABLE_SAVE, handleCustomSave);
    Emitter.on(EventConstant.EDITABLE_UP_KEYDOWN_SHORTCUT, handleUpNavigation);
    Emitter.on(
      EventConstant.EDITABLE_DOWN_KEYDOWN_SHORTCUT,
      handleDownNavigation,
    );
    Emitter.onMultiple(
      [EventConstant.ODM_GO_LEFT, EventConstant.ODM_GO_RIGHT],
      ({ event }) => handleLeftRightNavigation(event),
    );
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      Emitter.off(EventConstant.EDITABLE_SAVE_SHORTCUT);
      Emitter.off(EventConstant.EDITABLE_CANCEL_SHORTCUT);
      Emitter.off(EventConstant.ODM_CUSTOM_TABLE_SAVE);
      Emitter.off(EventConstant.EDITABLE_UP_KEYDOWN_SHORTCUT);
      Emitter.off(EventConstant.EDITABLE_DOWN_KEYDOWN_SHORTCUT);
      Emitter.offMultiple([
        EventConstant.ODM_GO_LEFT,
        EventConstant.ODM_GO_RIGHT,
      ]);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [
    EditableState.editableKeys,
    resetAutoCompute,
    sortedFilteredTable,
    // 移除nowAllInput从依赖数组，因为我们现在使用ref来跟踪它
    getAllInputs,
    editSaveHandler,
    EditableDispatch,
    editableTableFormRef,
    editableTableActionRef,
  ]);
};
