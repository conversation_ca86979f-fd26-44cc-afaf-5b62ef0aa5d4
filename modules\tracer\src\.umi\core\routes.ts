// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+runtime@3.5.34_react@16.14.0/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@uni/components/src/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "exact": true,
    "redirect": "/traceRecordList"
  },
  {
    "path": "/traceRecordList",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__traceRecord__index' */'@/pages/traceRecord/index'), loading: LoadingComponent})
  },
  {
    "path": "/askForPaySearch",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__askForPay__search__index' */'@/pages/askForPay/search/index'), loading: LoadingComponent})
  },
  {
    "path": "/ward",
    "routes": [
      {
        "path": "/ward/search",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ward__search__index' */'@/pages/ward/search/index'), loading: LoadingComponent})
      },
      {
        "path": "/ward/searchNotSignined",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ward__searchNotSignined__index' */'@/pages/ward/searchNotSignined/index'), loading: LoadingComponent})
      },
      {
        "path": "/ward/signin",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ward__signin__index' */'@/pages/ward/signin/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/mrRoom",
    "routes": [
      {
        "path": "/mrRoom/search",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__mrRoom__search__index' */'@/pages/mrRoom/search/index'), loading: LoadingComponent})
      },
      {
        "path": "/mrRoom/signin",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__mrRoom__signin__index' */'@/pages/mrRoom/signin/index'), loading: LoadingComponent})
      },
      {
        "path": "/mrRoom/statistic",
        "routes": [
          {
            "path": "/mrRoom/statistic/bySignDate",
            "exact": true,
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__mrRoom__statistic__bySignDate__index' */'@/pages/mrRoom/statistic/bySignDate/index'), loading: LoadingComponent})
          },
          {
            "path": "/mrRoom/statistic/byOutDate",
            "exact": true,
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__mrRoom__statistic__byOutDate__index' */'@/pages/mrRoom/statistic/byOutDate/index'), loading: LoadingComponent})
          }
        ]
      }
    ]
  },
  {
    "path": "/archive",
    "routes": [
      {
        "path": "/archive/search",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__archive__search__index' */'@/pages/archive/search/index'), loading: LoadingComponent})
      },
      {
        "path": "/archive/register",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__archive__register__index' */'@/pages/archive/register/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/seal",
    "routes": [
      {
        "path": "/seal/search",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__seal__search__index' */'@/pages/seal/search/index'), loading: LoadingComponent})
      },
      {
        "path": "/seal/register",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__seal__register__index' */'@/pages/seal/register/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/borrow",
    "routes": [
      {
        "path": "/borrow/search",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__borrow__search__index' */'@/pages/borrow/search/index'), loading: LoadingComponent})
      },
      {
        "path": "/borrow/borrowRegister",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__borrow__borrowRegister__index' */'@/pages/borrow/borrowRegister/index'), loading: LoadingComponent})
      },
      {
        "path": "/borrow/returnRegister",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__borrow__returnRegister__index' */'@/pages/borrow/returnRegister/index'), loading: LoadingComponent})
      },
      {
        "path": "/borrow/askForReturnSearch",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__borrow__askForReturn__search__index' */'@/pages/borrow/askForReturn/search/index'), loading: LoadingComponent})
      },
      {
        "path": "/borrow/borrowTasks",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__borrow__borrowTasks__index' */'@/pages/borrow/borrowTasks/index'), loading: LoadingComponent})
      },
      {
        "path": "/borrow/userBorrowTasks",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__borrow__userBorrowTasks__index' */'@/pages/borrow/userBorrowTasks/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/print",
    "routes": [
      {
        "path": "/print/search",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__print__search__index' */'@/pages/print/search/index'), loading: LoadingComponent})
      },
      {
        "path": "/print/register",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__print__register__index' */'@/pages/print/register/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/dmrSignOut",
    "routes": [
      {
        "path": "/dmrSignOut/search",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dmrSignOut__search__index' */'@/pages/dmrSignOut/search/index'), loading: LoadingComponent})
      },
      {
        "path": "/dmrSignOut/discharge",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__dmrSignOut__discharge__index' */'@/pages/dmrSignOut/discharge/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/mrRoomSignInAnalysis",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__analysis__index' */'@/pages/analysis/index'), loading: LoadingComponent})
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
