import { useState, useRef, useEffect } from 'react';
import _ from 'lodash';

/**
 * 表格排序和过滤hook
 * @param editableValue 可编辑表格数据
 */
export const useSorterAndFilter = (editableValue: any[] = []) => {
  const [sorterInfo, setSorterInfo] = useState<any>({});
  const sortedFilteredTable = useRef<any[]>([]);

  useEffect(() => {
    let sortedData = [...(editableValue || [])];
    if (sorterInfo.columnKey && sorterInfo.order) {
      sortedData.sort((a, b) => {
        return _.isString(a[sorterInfo.columnKey])
          ? sorterInfo.order === 'ascend'
            ? a[sorterInfo.columnKey]?.localeCompare(b[sorterInfo.columnKey])
            : b[sorterInfo.columnKey]?.localeCompare(a[sorterInfo.columnKey])
          : sorterInfo.order === 'ascend'
          ? a[sorterInfo.columnKey] - b[sorterInfo.columnKey]
          : b[sorterInfo.columnKey] - a[sorterInfo.columnKey];
      });
    }
    sortedFilteredTable.current = sortedData;
  }, [sorterInfo, editableValue]);

  return { sorterInfo, setSorterInfo, sortedFilteredTable };
};
