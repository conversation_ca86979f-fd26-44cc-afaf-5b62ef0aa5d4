import React, { useEffect, useMemo, useState } from 'react';
import './index.less';
import { UniSelect, UniTable } from '@uni/components/src';
import { hospitalMenuColumns } from '@/pages/configuration/menu/columns';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { useModel } from 'umi';
import IconBtn from '@uni/components/src/iconBtn';
import {
  Card,
  Form,
  Input,
  Modal,
  Button,
  Checkbox,
  InputNumber,
  message,
  Tag,
  Space,
  Select,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import { PlusCircleTwoTone } from '@ant-design/icons';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { usersColumns } from '../columns';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { filterAdminWhenHasNotPermission } from '@/pages/configuration/utils';

const UsersIndex = () => {
  const { globalState } = useModel('@@qiankunStateForSlave');
  const { initialState }: any = useModel('@@initialState');

  const [form] = Form.useForm();
  const [employeeDictData, setEmployeeDictData] = useState([]);
  const [usersTableDataSource, setUsersTableDataSource] = useState([]);
  const [usersSubSystemEdit, setUsersSubSystemEdit] = useState(false);
  const [usersSubSystemAdd, setUsersSubSystemAdd] = useState(false);
  const [usersSubSystemEditPwd, setUsersSubSystemEditPwd] = useState(false);
  useEffect(() => {
    Emitter.on(ConfigurationEvents.USERS_SUBSYSTEM_EDIT, (record) => {
      if (record) {
        setUsersSubSystemEdit(true);
        form.setFieldsValue(record);
      }
    });
    Emitter.on(ConfigurationEvents.USERS_SUBSYSTEM_UNLOCK, (record) => {
      if (record) {
        UnlockUsersReq({ Id: record?.Id });
      }
    });
    Emitter.on(ConfigurationEvents.USERS_SUBSYSTEM_EDIT_PWD, (record) => {
      if (record) {
        setUsersSubSystemEditPwd(true);
        form.setFieldsValue(record);
      }
    });
    Emitter.on(ConfigurationEvents.USERS_SUBSYSTEM_RESET_PWD, (record) => {
      if (record) {
        resetPwdUsersReq({ Id: record?.Id });
      }
    });
    Emitter.on(ConfigurationEvents.USERS_SUBSYSTEM_DELETE, (record) => {
      if (record) {
        DeleteUsersReq({ Id: record?.Id });
      }
    });

    return () => {
      Emitter.off(ConfigurationEvents.USERS_SUBSYSTEM_EDIT);
      Emitter.off(ConfigurationEvents.USERS_SUBSYSTEM_UNLOCK);
      Emitter.off(ConfigurationEvents.USERS_SUBSYSTEM_DELETE);
      Emitter.off(ConfigurationEvents.USERS_SUBSYSTEM_EDIT_PWD);
      Emitter.off(ConfigurationEvents.USERS_SUBSYSTEM_RESET_PWD);
    };
  }, [usersTableDataSource]);

  useEffect(() => {
    GetUsersReq();
  }, []);

  const { loading: GetUsersLoading, run: GetUsersReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/IdentitySys/GetUsers', {
        method: 'POST',
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setUsersTableDataSource(
            response?.data.map((i) => {
              let Employee = employeeDictData?.find(
                (item) => item.Code === i?.EmployeeCode,
              );
              return { ...i, EmployeeName: Employee?.Name };
            }),
          );
        } else {
          setUsersTableDataSource([]);
        }
      },
    },
  );

  useEffect(() => {
    if (globalState?.dictData) {
      setEmployeeDictData(globalState?.dictData?.['Employee']);
    }
  }, [globalState?.dictData]);

  useEffect(() => {
    if (employeeDictData?.length && usersTableDataSource?.length) {
      setUsersTableDataSource(
        usersTableDataSource.map((i) => {
          let Employee = employeeDictData?.find(
            (item) => item.Code === i?.EmployeeCode,
          );
          return { ...i, EmployeeName: Employee?.Name };
        }),
      );
    }
  }, [employeeDictData]);

  const { run: editUserReq } = useRequest(
    (values) => {
      return uniCommonService('Api/Sys/IdentitySys/EditUser', {
        method: 'POST',
        requestType: 'json',
        data: {
          ...values,
          hospCodes:
            typeof values?.Hospitals === 'string'
              ? [values?.Hospitals]
              : values?.Hospitals,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
    },
  );

  const { run: createUserReq } = useRequest(
    (values) => {
      return uniCommonService('Api/Sys/IdentitySys/CreateUser', {
        method: 'POST',
        requestType: 'json',
        data: values,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setUsersSubSystemAdd(false);
          GetUsersReq();
        }
      },
    },
  );

  const { loading: resetPwdUsersLoading, run: resetPwdUsersReq } = useRequest(
    (data) => {
      return uniCommonService('Api/Sys/IdentitySys/ResetUserPwd', {
        method: 'POST',
        requestType: 'json',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.statusCode === 200) {
          message.success('重置密码成功');
          setUsersSubSystemAdd(false);
        }
      },
    },
  );

  const { run: editPwdUsersReq } = useRequest(
    (data) => {
      return uniCommonService('Api/Sys/IdentitySys/EditUserPwd', {
        method: 'POST',
        requestType: 'json',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.statusCode === 200) {
          message.success('修改密码成功');
          setUsersSubSystemEditPwd(false);
        }
      },
    },
  );

  const onUserEdit = async (values) => {
    let editUserResponse = await editUserReq(values);
    if (editUserResponse?.code === 0 && editUserResponse?.statusCode === 200) {
      GetUsersReq();
    }
  };

  const { run: DeleteUsersReq } = useRequest(
    (data) => {
      return uniCommonService('Api/Sys/IdentitySys/DeleteUser', {
        method: 'POST',
        requestType: 'json',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.statusCode === 200) {
          message.success('删除成功');
          let index = usersTableDataSource?.findIndex(
            (item) => item.Id === params[0]?.Id,
          );
          let data = usersTableDataSource?.slice();
          data.splice(index, 1);
          setUsersTableDataSource(data);
        }
      },
    },
  );
  const { run: UnlockUsersReq } = useRequest(
    (data) => {
      return uniCommonService('Api/Sys/IdentitySys/UnlockUser', {
        method: 'POST',
        requestType: 'json',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response?.statusCode === 200) {
          message.success('解锁成功');
        }
      },
    },
  );

  return (
    <Card
      title={'用户列表'}
      extra={
        <Button
          onClick={(e) => {
            setUsersSubSystemAdd(true);
          }}
        >
          <PlusCircleTwoTone />
          新增用户
        </Button>
      }
    >
      <div id={'users-config-container'} className={'users-config-container'}>
        <UniTable
          id={`users-configuration`}
          rowKey={'Id'}
          bordered={true}
          loading={GetUsersLoading}
          columns={usersColumns(globalState?.dictData?.Hospital)}
          // dictionaryData={globalState?.dictData}
          dataSource={usersTableDataSource?.filter((item) => {
            return filterAdminWhenHasNotPermission(
              item,
              'UserName',
              initialState?.userInfo?.Roles ?? [],
              'admin',
            );
          })}
          clickable={false}
        />

        <Modal
          title="修改用户"
          open={usersSubSystemEdit}
          onOk={() => {
            setUsersSubSystemEdit(false);
            onUserEdit(form.getFieldsValue());
          }}
          onCancel={() => {
            setUsersSubSystemEdit(false);
          }}
          destroyOnClose={true}
          getContainer={document.getElementById('users-config-container')}
          okButtonProps={{
            htmlType: 'submit',
          }}
        >
          <UserSubSystemEdit form={form} />
        </Modal>
        <Modal
          title="新增用户"
          open={usersSubSystemAdd}
          onOk={() => {
            createUserReq(form.getFieldsValue());
          }}
          onCancel={() => {
            setUsersSubSystemAdd(false);
          }}
          destroyOnClose={true}
          getContainer={document.getElementById('users-config-container')}
          okButtonProps={{
            htmlType: 'submit',
          }}
        >
          <UserSubSystemCreate form={form} />
        </Modal>
        <Modal
          title="修改用户密码"
          open={usersSubSystemEditPwd}
          onOk={() => {
            editPwdUsersReq(form.getFieldsValue());
          }}
          onCancel={() => {
            setUsersSubSystemEditPwd(false);
          }}
          destroyOnClose={true}
          getContainer={document.getElementById('users-config-container')}
          okButtonProps={{
            htmlType: 'submit',
          }}
        >
          <UserSubSystemEditPwd form={form} />
        </Modal>
      </div>
    </Card>
  );
};

interface UserSubSystemEditProps {
  form: any;
}

export const UserSubSystemEdit = (props: UserSubSystemEditProps) => {
  const { globalState } = useModel('@@qiankunStateForSlave');
  const dictData = globalState?.dictData;
  const { Option } = Select;
  const [employeeDictData, setEmployeeDictData] = useState([]);
  useEffect(() => {
    if (globalState?.dictData) {
      setEmployeeDictData(globalState?.dictData?.['Employee']);
    }
  }, [globalState?.dictData]);
  return (
    <div className={'user-subsystem-edit-container'}>
      <Form
        form={props?.form}
        preserve={false}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        autoComplete="off"
      >
        <Form.Item label="Id" name={'Id'} hidden>
          <Input />
        </Form.Item>
        <Form.Item label="用户名" name={'UserName'}>
          <Input disabled={true} />
        </Form.Item>

        <Form.Item label="工号" name={'EmployeeCode'} required>
          <Select
            placeholder={'请选择'}
            maxTagCount={'responsive'}
            showSearch={true}
            filterOption={(inputValue, option) => {
              return (
                (option &&
                  option.children
                    ?.toString()
                    ?.toLowerCase()
                    ?.indexOf(inputValue.toLowerCase()) !== -1) ||
                option?.value
                  ?.toString()
                  ?.toLowerCase()
                  ?.indexOf(inputValue?.toLowerCase()) !== -1 ||
                pinyinInitialSearch(
                  option.children?.toString()?.toLowerCase(),
                  inputValue.toLowerCase(),
                )
              );
            }}
          >
            {employeeDictData &&
              employeeDictData.map((item) => {
                return (
                  <Option
                    key={uuidv4()}
                    title={item['Name']}
                    value={item['Code']}
                  >
                    {item['Code']}：{item['Name']}
                  </Option>
                );
              })}
          </Select>
        </Form.Item>

        <Form.Item label="姓名" name={'Name'} required>
          <Input />
        </Form.Item>

        <Form.Item label="用户组" name={'RoleNames'}>
          <UniSelect
            dataSource={dictData?.['RoleWithDesc']}
            placeholder={'请选择用户组'}
            optionNameKey={'Name'}
            optionValueKey={'Code'}
            mode={'multiple'}
            maxTagCount={'responsive'}
          />
        </Form.Item>

        <Form.Item label="所属院区" name={'HospCode'}>
          <UniSelect
            dataSource={dictData?.['Hospital']}
            placeholder={'请选择院区'}
            optionNameKey={'Name'}
            optionValueKey={'Code'}
            mode={'multiple'}
            maxTagCount={'responsive'}
          />
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.HospCode !== curValues.HospCode
          }
        >
          {({ getFieldValue }) => {
            console.log(getFieldValue('HospCode'));
            return (
              <>
                <Form.Item label="所属科室" name="CliDepts">
                  <UniSelect
                    dataSource={dictData?.['Hierarchies']?.filter(
                      (item) =>
                        (getFieldValue('HospCode') || []).includes(
                          item.HospCode,
                        ) && item.HierarchyType === '1',
                    )}
                    placeholder={'请选择科室'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>

                <Form.Item label="所属病区" name="Wards">
                  <UniSelect
                    dataSource={dictData?.['Hierarchies']?.filter(
                      (item) =>
                        (getFieldValue('HospCode') || []).includes(
                          item.HospCode,
                        ) && item.HierarchyType === '2',
                    )}
                    placeholder={'请选择病区'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>

                <Form.Item label="所属医疗组" name="MedTeams">
                  <UniSelect
                    dataSource={dictData?.['Hierarchies']?.filter(
                      (item) =>
                        (getFieldValue('HospCode') || []).includes(
                          item.HospCode,
                        ) && item.HierarchyType === '3',
                    )}
                    placeholder={'请选择医疗组'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>

                <Form.Item label="所属学科" name="MajorPerfDepts">
                  <UniSelect
                    dataSource={dictData?.['MajorPerfDepts']}
                    placeholder={'请选择学科'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>
                <Form.Item label="所属绩效科室" name="PerfDepts">
                  <UniSelect
                    dataSource={dictData?.['PerfDepts']}
                    placeholder={'请选择绩效科室'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>
              </>
            );
          }}
        </Form.Item>
      </Form>
    </div>
  );
};

export const UserSubSystemCreate = (props: UserSubSystemEditProps) => {
  const { globalState } = useModel('@@qiankunStateForSlave');
  const dictData = globalState?.dictData;
  const { Option } = Select;
  const [employeeDictData, setEmployeeDictData] = useState([]);
  useEffect(() => {
    if (globalState?.dictData) {
      setEmployeeDictData(globalState?.dictData?.['Employee']);
    }
  }, [globalState?.dictData]);
  return (
    <div className={'user-subsystem-create-container'}>
      <Form
        form={props?.form}
        preserve={false}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <Form.Item
          label="用户名"
          name={'UserName'}
          rules={[{ required: true }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="工号"
          name={'EmployeeCode'}
          rules={[{ required: true }]}
        >
          <Select
            placeholder={'请选择'}
            maxTagCount={'responsive'}
            showSearch={true}
            filterOption={(inputValue, option) => {
              return (
                (option &&
                  option.children
                    ?.toString()
                    ?.toLowerCase()
                    ?.indexOf(inputValue.toLowerCase()) !== -1) ||
                option?.value
                  ?.toString()
                  ?.toLowerCase()
                  ?.indexOf(inputValue?.toLowerCase()) !== -1 ||
                pinyinInitialSearch(
                  option.children?.toString()?.toLowerCase(),
                  inputValue.toLowerCase(),
                )
              );
            }}
          >
            {employeeDictData &&
              employeeDictData.map((item) => {
                return (
                  <Option
                    key={uuidv4()}
                    title={item['Name']}
                    value={item['Code']}
                  >
                    {item['Code']}：{item['Name']}
                  </Option>
                );
              })}
          </Select>
        </Form.Item>

        <Form.Item label="姓名" name={'Name'} required>
          <Input />
        </Form.Item>

        <Form.Item
          label="密码"
          name={'Password'}
          rules={[{ required: true }]}
          hasFeedback
        >
          <Input.Password />
        </Form.Item>

        <Form.Item
          label="确认密码"
          name={'ConfirmPassword'}
          rules={[
            { required: true },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('Password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次密码不一致!'));
              },
            }),
          ]}
          hasFeedback
        >
          <Input.Password />
        </Form.Item>

        <Form.Item
          label="用户组"
          name={'RoleNames'}
          rules={[{ required: true }]}
        >
          <UniSelect
            dataSource={dictData?.['RoleWithDesc']}
            placeholder={'请选择用户组'}
            optionNameKey={'Name'}
            optionValueKey={'Code'}
            mode={'multiple'}
            maxTagCount={'responsive'}
          />
        </Form.Item>

        <Form.Item
          label="所属院区"
          name={'HospCode'}
          rules={[{ required: true }]}
        >
          <UniSelect
            dataSource={dictData?.['Hospital']}
            placeholder={'请选择院区'}
            optionNameKey={'Name'}
            optionValueKey={'Code'}
            mode={'multiple'}
            maxTagCount={'responsive'}
          />
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.HospCode !== curValues.HospCode
          }
        >
          {({ getFieldValue }) => {
            return (
              <>
                <Form.Item
                  label="所属科室"
                  name="CliDepts"
                  style={{ width: '100%' }}
                  labelCol={{ span: 6 }}
                  wrapperCol={{ span: 18 }}
                >
                  <UniSelect
                    dataSource={dictData?.['Hierarchies']?.filter(
                      (item) =>
                        (getFieldValue('HospCode') || []).includes(
                          item.HospCode,
                        ) && item.HierarchyType === '1',
                    )}
                    placeholder={'请选择科室'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>

                <Form.Item label="所属病区" name="Wards">
                  <UniSelect
                    dataSource={dictData?.['Hierarchies']?.filter(
                      (item) =>
                        (getFieldValue('HospCode') || []).includes(
                          item.HospCode,
                        ) && item.HierarchyType === '2',
                    )}
                    placeholder={'请选择病区'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>

                <Form.Item
                  label="所属医疗组"
                  name="MedTeams"
                  style={{ width: '100%' }}
                  labelCol={{ span: 6 }}
                  wrapperCol={{ span: 18 }}
                >
                  <UniSelect
                    dataSource={dictData?.['Hierarchies']?.filter(
                      (item) =>
                        (getFieldValue('HospCode') || []).includes(
                          item.HospCode,
                        ) && item.HierarchyType === '3',
                    )}
                    placeholder={'请选择医疗组'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>

                <Form.Item
                  label="所属学科"
                  name="MajorPerfDepts"
                  style={{ width: '100%' }}
                  labelCol={{ span: 6 }}
                  wrapperCol={{ span: 18 }}
                >
                  <UniSelect
                    dataSource={dictData?.['MajorPerfDepts']}
                    placeholder={'请选择学科'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>
                <Form.Item
                  label="所属绩效科室"
                  name="PerfDepts"
                  style={{ width: '100%' }}
                  labelCol={{ span: 6 }}
                  wrapperCol={{ span: 18 }}
                >
                  <UniSelect
                    dataSource={dictData?.['PerfDepts']}
                    placeholder={'请选择绩效科室'}
                    optionNameKey={'Name'}
                    optionValueKey={'Code'}
                    mode={'multiple'}
                    maxTagCount={'responsive'}
                  />
                </Form.Item>
              </>
            );
          }}
        </Form.Item>

        <Form.Item label="账号有效天数" name="expirationPeriod">
          <InputNumber min={0} />
        </Form.Item>

        <Form.Item
          name="isTemporaryAccount"
          valuePropName="checked"
          wrapperCol={{ offset: 6 }}
        >
          <Checkbox>是否临时账号</Checkbox>
        </Form.Item>
      </Form>
    </div>
  );
};

export const UserSubSystemEditPwd = (props: UserSubSystemEditProps) => {
  return (
    <div className={'hospital-subsystem-edit-pwd-container'}>
      <Form
        form={props?.form}
        preserve={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <Form.Item label="Id" name={'Id'} hidden>
          <Input />
        </Form.Item>
        <Form.Item label="用户名" name={'UserName'}>
          <Input disabled={true} />
        </Form.Item>
        <Form.Item
          label="密码"
          name={'Password'}
          rules={[{ required: true }]}
          hasFeedback
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          label="确认密码"
          name={'ConfirmPassword'}
          rules={[
            { required: true },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('Password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次密码不一致!'));
              },
            }),
          ]}
          hasFeedback
        >
          <Input.Password />
        </Form.Item>
      </Form>
    </div>
  );
};

export default UsersIndex;
