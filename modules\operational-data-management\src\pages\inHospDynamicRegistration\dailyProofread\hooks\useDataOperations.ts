import { useState } from 'react';
import { message } from 'antd';
import { useDispatch } from 'umi';
import { isRespErr } from '@/utils/widgets';
import dayjs from 'dayjs';
import _ from 'lodash';
import { ReqActionType } from '@/Constants';
import {
  IDeptpatientAmtListItem,
  IStatsDmrCardCntByDeptAndDailyListItem,
} from '../interface';
import { TableAction } from '@uni/reducers/src/tableReducer';

/**
 * 比较两个数字类型字段的函数
 */
export const compareNumberFields = (
  baseValue: any,
  compareValue: any,
  fieldName: string,
) => {
  // 如果比较值为null或undefined，跳过比较
  if (compareValue === null || compareValue === undefined) {
    return {
      hasDiscrepancy: false,
      displayValue: baseValue,
    };
  }

  // 如果两个值不同，返回带比较值的显示文本
  if (baseValue !== compareValue) {
    return {
      hasDiscrepancy: true,
      displayValue: `${baseValue}（${compareValue}）`,
    };
  }

  // 否则返回原始值
  return {
    hasDiscrepancy: false,
    displayValue: baseValue,
  };
};

/**
 * 数据操作相关hook
 */
export const useDataOperations = ({
  TableDispatch,
  EditableDispatch,
  tableParams,
  dictData,
  loadData,
}) => {
  const dispatch = useDispatch();

  // 通用操作请求
  const reqActionReq = async (
    reqData: any,
    reqType: ReqActionType,
    reqId = undefined,
    skipLoading = false,
  ) => {
    let res = await dispatch({
      type: 'global/fetchNormal',
      payload: {
        name: `DeptInpatientAmt/${reqType}`,
        requestParams: {
          url:
            reqType === ReqActionType.Update && reqId
              ? `Api/Dyn-ddr/DeptInpatientAmt/${reqType}/${reqId.id}`
              : `Api/Dyn-ddr/DeptInpatientAmt/${reqType}`,
          method: 'POST',
          params: reqType === ReqActionType.Update && reqId ? reqId : undefined,
          data: reqData,
          dataType: 'dyn-ddr',
          requestType: reqType === ReqActionType.Import ? 'form' : 'json',
        },
      },
    });

    if (!isRespErr(res)) {
      if (reqType === ReqActionType.Update) {
        if (!skipLoading) {
          EditableDispatch({
            type: TableAction.editableKeysChange,
            payload: {
              editableKeys: [],
            },
          });
        }
        // 根据skipLoading参数决定是否传入skipLoading=true
        loadData(tableParams, skipLoading);
        // EditableDispatch({
        //   type: TableAction.editableSingleRecordChange,
        //   payload: {
        //     key: 'Id',
        //     value: res.data,
        //   },
        // });
        message.success('保存成功');
      } else {
        message.success('操作成功');
        loadData(tableParams);
      }
      return true;
    } else if (reqType === ReqActionType.Update) {
      return false;
    }
  };

  // 编辑保存处理
  const editSaveHandler = async (
    recordKey,
    data,
    needAutoFetch = false,
    skipLoading = false,
  ) => {
    if (recordKey?.toString()?.includes('new')) {
      await reqActionReq(
        {
          ..._.omit(data?.[recordKey?.toString()] ?? data, 'Id'),
          HospCode: dictData.DynDepts?.find(
            (opt) => opt?.Code === tableParams?.DeptCode,
          )?.ExtraProperties?.HospCode,
          DeptCode: tableParams?.DeptCode,
          ExactDate: dayjs(
            (data?.[recordKey?.toString()] ?? data)?.ExactDate,
          )?.format('YYYY-MM-DD'),
        },
        ReqActionType.Create,
        undefined,
        skipLoading,
      );
    } else {
      await reqActionReq(
        data?.[recordKey?.toString()] ?? data,
        ReqActionType.Update,
        {
          id: recordKey?.toString(),
        },
        skipLoading,
      );
    }

    if (needAutoFetch) {
      loadData(tableParams);
    }
  };

  // 合并和比较两个数据源
  const mergeAndCompareData = (
    baseData: IDeptpatientAmtListItem[],
    compareData: IStatsDmrCardCntByDeptAndDailyListItem[],
  ) => {
    // 复制基础数据
    return baseData.map((baseItem) => {
      const result = { ...baseItem, hasDiscrepancy: false };

      // 查找对应的比较数据
      const compareItem = compareData.find(
        (item) =>
          item.HospCode === baseItem.HospCode &&
          item.DeptCode === baseItem.DeptCode &&
          dayjs(item.ExactDate).format('YYYY-MM-DD') ===
            dayjs(baseItem.ExactDate).format('YYYY-MM-DD'),
      );

      if (compareItem) {
        // 需要比较的字段列表
        const fieldsToCompare = [
          'InHospCnt',
          'TransInCnt',
          'TransOutCnt',
          'TotalOutHospCnt',
          'OutHospCnt',
          'CuredCnt',
          'ImprovedCnt',
          'NotCuredCnt',
          'DeathCnt',
          'OtherCnt1',
          'OtherCnt2',
          'OtherCnt3',
          'OtherCnt4',
          'OtherCnt5',
          'OtherCnt6',
          'OtherCnt7',
          'OtherCnt8',
        ];

        // 比较每个字段
        fieldsToCompare.forEach((field) => {
          const comparison = compareNumberFields(
            baseItem[field],
            compareItem[field],
            field,
          );

          // 如果compareItem[field]有值（包括0），就将其赋值给result[`${field}_compare`]
          if (compareItem[field] !== null && compareItem[field] !== undefined) {
            result[`${field}_compare`] = compareItem[field];
          }

          // 如果有差异，标记
          if (comparison.hasDiscrepancy) {
            result.hasDiscrepancy = true;
            result[`${field}_hasDiscrepancy`] = true;
          }
        });
      }

      return result;
    });
  };

  return {
    reqActionReq,
    editSaveHandler,
    mergeAndCompareData,
  };
};
