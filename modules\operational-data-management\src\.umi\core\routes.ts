// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+runtime@3.5.34_react@16.14.0/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@uni/components/src/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "exact": true,
    "redirect": "/in/byDay"
  },
  {
    "path": "/workLoad/medicalTechWorkload",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__medicalTechWorkload__index' */'@/pages/medicalTechWorkload/index'), loading: LoadingComponent})
  },
  {
    "path": "/workLoad/hospWorkloadItemCheck",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__medLab__index' */'@/pages/medLab/index'), loading: LoadingComponent})
  },
  {
    "path": "/workLoad/workloadItemCheck",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__workloadItem__index' */'@/pages/workloadItem/index'), loading: LoadingComponent})
  },
  {
    "path": "/in/byDay",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__inHospDynamicRegistration__editByDate__index' */'@/pages/inHospDynamicRegistration/editByDate/index'), loading: LoadingComponent})
  },
  {
    "path": "/in/byDayWithWard",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__inHospDynamicRegistration__editByDateWithWard__index' */'@/pages/inHospDynamicRegistration/editByDateWithWard/index'), loading: LoadingComponent})
  },
  {
    "path": "/in/byDept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__inHospDynamicRegistration__editByDept__index' */'@/pages/inHospDynamicRegistration/editByDept/index'), loading: LoadingComponent})
  },
  {
    "path": "/in/byWard",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__inHospDynamicRegistration__editByWard__index' */'@/pages/inHospDynamicRegistration/editByWard/index'), loading: LoadingComponent})
  },
  {
    "path": "/in/hierarchyBed",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__inHospDynamicRegistration__hierarchyBed__index' */'@/pages/inHospDynamicRegistration/hierarchyBed/index'), loading: LoadingComponent})
  },
  {
    "path": "/in/proofread",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__inHospDynamicRegistration__proofread__index' */'@/pages/inHospDynamicRegistration/proofread/index'), loading: LoadingComponent})
  },
  {
    "path": "/in/dailyProofread",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__inHospDynamicRegistration__dailyProofread__index' */'@/pages/inHospDynamicRegistration/dailyProofread/index'), loading: LoadingComponent})
  },
  {
    "path": "/obs/byDay",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__observeDynamicRegistration__editByDate__index' */'@/pages/observeDynamicRegistration/editByDate/index'), loading: LoadingComponent})
  },
  {
    "path": "/obs/byDept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__observeDynamicRegistration__editByDept__index' */'@/pages/observeDynamicRegistration/editByDept/index'), loading: LoadingComponent})
  },
  {
    "path": "/obs/hierarchyBed",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__observeDynamicRegistration__hierarchyBed__index' */'@/pages/observeDynamicRegistration/hierarchyBed/index'), loading: LoadingComponent})
  },
  {
    "path": "/out/byDay",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__outPatientDynamicRegistration__editByDate__index' */'@/pages/outPatientDynamicRegistration/editByDate/index'), loading: LoadingComponent})
  },
  {
    "path": "/out/byDept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__outPatientDynamicRegistration__editByDept__index' */'@/pages/outPatientDynamicRegistration/editByDept/index'), loading: LoadingComponent})
  },
  {
    "path": "/outDoctor/byDay",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__outPatientDoctorDynamicRegistration__editByDate__index' */'@/pages/outPatientDoctorDynamicRegistration/editByDate/index'), loading: LoadingComponent})
  },
  {
    "path": "/outDoctor/byDept",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__outPatientDoctorDynamicRegistration__editByDept__index' */'@/pages/outPatientDoctorDynamicRegistration/editByDept/index'), loading: LoadingComponent})
  },
  {
    "path": "/inHospManagement",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__inHospDynamicManagement__index' */'@/pages/inHospDynamicManagement/index'), loading: LoadingComponent})
  },
  {
    "path": "/outPatientManagement",
    "exact": true,
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__outPatientManagement__index' */'@/pages/outPatientManagement/index'), loading: LoadingComponent})
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
