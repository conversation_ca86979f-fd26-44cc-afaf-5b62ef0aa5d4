import { useState, useRef, useEffect } from 'react';
import { useDebounceFn } from 'ahooks';
import _ from 'lodash';
import { isRespErr } from '@/utils/widgets';
import {
  autoCalculateHandler,
  autoComputeRef,
  getCalculationKeys,
} from '@/utils/util';

/**
 * 自动计算hook
 * @param editableTableFormRef 表单引用
 */
export const useAutoCalculation = (editableTableFormRef) => {
  const [autoCalculationKeys, setAutoCalculationKeys] = useState<string[]>([]);
  const isAutoComputeCntInput = useRef<any>(undefined);
  const getCalculationKeysDone = useRef(false);

  // 初始化获取需要自动计算的字段
  useEffect(() => {
    const fetchCalculationKeys = async () => {
      let res = await getCalculationKeys('Inpatient');
      if (!isRespErr(res)) {
        isAutoComputeCntInput.current = autoComputeRef(Object.keys(res?.data));
        setAutoCalculationKeys(Object.keys(res?.data));
        getCalculationKeysDone.current = true;
      }
    };
    fetchCalculationKeys();
  }, []);

  // 使用防抖发送自动计算请求
  const { run: autoCalculateDebounceFn } = useDebounceFn(
    (keys, record, formRef, isAutoComputInputs) => {
      autoCalculateHandler(
        keys,
        _.omit(record, ['HospCode']),
        formRef,
        'DeptInpatientAmt',
        isAutoComputInputs,
      );
    },
    { wait: 250 },
  );

  return {
    autoCalculationKeys,
    isAutoComputeCntInput,
    getCalculationKeysDone,
    autoCalculateDebounceFn,
    resetAutoCompute: () => {
      isAutoComputeCntInput.current = autoComputeRef(autoCalculationKeys);
    },
  };
};
