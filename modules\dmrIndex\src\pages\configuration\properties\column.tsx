import { PropertyItem } from '@/pages/configuration/interfaces';
import { PropertyItemConstants } from '@/pages/configuration/constants';

export const commonTableColumnProperty = {
  bordered: false,
};

export const readonlyDataIndex = [];

export const TableColumnFullProperties: PropertyItem[] = [
  {
    key: 'order',
    label: '顺序',
    component: 'Input',
    width: 70,
    readonly: true,
    fieldProps: {
      allowClear: false,
    },
  },
  {
    key: 'dataIndex',
    label: '表格列字段',
    component: 'Input',
    valueType: 'string',
    width: 150,
    fieldProps: {
      disabled: true,
    },
  },
  {
    key: 'title',
    label: '标题',
    component: 'Input',
    valueType: 'string',
    width: 200,
    fieldProps: {
      allowClear: false,
    },
  },
  {
    key: 'visible',
    label: '显示',
    width: 60,
    component: 'Switch',
    valueType: 'switch',
    fieldProps: {},
  },
  {
    key: 'readonly',
    label: '只读',
    width: 60,
    component: 'Switch',
    valueType: 'switch',
    fieldProps: {},
  },
  {
    key: 'align',
    label: '列对齐',
    component: 'Select',
    valueEnum: PropertyItemConstants.ColumnAlignOptions,
    valueType: 'select',
    fieldProps: {},
  },
  {
    key: 'width',
    label: '宽度',
    component: 'Input',
    valueType: 'digit',
    fieldProps: {
      min: 40,
      keyboard: false,
      precision: 0,
      controls: false,
    },
  },
  {
    key: 'fixed',
    label: '列固定方式',
    component: 'Select',
    valueEnum: PropertyItemConstants.ColumnFixedOptions,
    valueType: 'select',
    fieldProps: {},
  },
  {
    key: 'conditionDictionaryKey',
    label: '字典库Module',
    component: 'Input',
    valueType: 'string',
    width: 150,
    fieldProps: {
      allowClear: false,
    },
  },
  {
    key: 'conditionDictionaryGroup',
    label: '字典库Group',
    component: 'Input',
    valueType: 'string',
    width: 150,
    fieldProps: {
      allowClear: false,
    },
  },
  {
    key: 'labelFormat',
    label: '字典库显示格式',
    component: 'Input',
    valueType: 'string',
    width: 150,
    fieldProps: {
      allowClear: false,
    },
  },
];

export const configurableDataIndex = TableColumnFullProperties?.map(
  (item) => item.key,
)?.concat('extraProps');

export const IcdeTableColumnsBlockProperties: { [key: string]: string[] } = {
  IcdeExtra: ['title'],
  RowSelection: ['title'],
  operation: ['title'],
};

export const OperTableColumnsBlockProperties: { [key: string]: string[] } = {
  OperExtra: ['title'],
  RowSelection: ['title'],
  operation: ['title'],
};
