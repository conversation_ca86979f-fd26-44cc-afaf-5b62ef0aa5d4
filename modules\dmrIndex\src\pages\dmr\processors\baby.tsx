import {
  DmrInsuranceHqmsIcdeItem,
  IcdeOperResp,
  IcdeResp,
  IcdeRespItem,
} from '@/pages/dmr/network/interfaces';
import cloneDeep from 'lodash/cloneDeep';
import isNil from 'lodash/isNil';
import { v4 as uuidv4 } from 'uuid';
import { isEmptyValues } from '@uni/utils/src/utils';

export const cardBabysResponseProcessor = (
  formFieldValue,
  babys: any[],
  babyIcdes: IcdeRespItem[],
) => {
  let babyInfos = babys
    ?.slice()
    ?.sort((a, b) => (a?.BabySort ?? 1) - (b?.BabySort ?? 1));

  babyInfos?.slice(1, babyInfos?.length)?.forEach((babyItem, index) => {
    // 从2 开始
    formFieldValue[`BabyBw${index + 2}`] = babyItem?.BabyWeight ?? '';
  });

  babyInfos?.forEach((babyItem, index) => {
    babyItem['BabyNo'] = babyItem['BabyNo'] ?? formFieldValue?.['PatNo'];

    babyItem['UmbilicalCord'] = babyItem['UmbilicalCord']?.split(',');

    // 新生儿诊断数据
    let currentBabyIcdes = babyIcdeTableResponseProcessor(
      babyIcdes,
      babyItem?.BabySort,
    );

    babyItem['icde-table'] = currentBabyIcdes;

    babyItem['icdeTable'] = cloneDeep(babyItem['icde-table']?.slice());
  });

  // 新生儿附页
  formFieldValue['babies'] = babyInfos;
  formFieldValue['babiesModalData'] = cloneDeep(babyInfos);

  return formFieldValue;
};

export const saveBabysParamProcessor = (formFieldValues) => {
  let babyData = [];

  if (!isEmptyValues(formFieldValues?.BabyBw)) {
    babyData = [
      ...babyData,
      {
        BabySort: 1,
        BabyWeight: formFieldValues?.BabyBw,
      },
    ];
  }

  Object.keys(formFieldValues).forEach((key) => {
    if (/BabyBw[1-9]/.test(key)) {
      if (!isEmptyValues(formFieldValues?.[key])) {
        babyData.push({
          BabySort: babyData?.length + 1,
          BabyWeight: formFieldValues?.[key],
        });
      }
    }
  });

  return babyData;
};

export const babyIcdeTableResponseProcessor = (
  babyIcdes: IcdeRespItem[],
  babySort: number,
) => {
  // 诊断表格
  let icdeTableData = [];

  let currentBabyIcdes = babyIcdes?.filter(
    (item) => item?.BabySort === babySort,
  );

  if (currentBabyIcdes?.length > 0) {
    for (let item of currentBabyIcdes) {
      item['id'] = item['IcdeId'] ?? item['Id'];
      icdeTableData.push(item);
    }
  }

  return icdeTableData?.sort(
    (a, b) => (a?.IcdeSort ?? 999) - (b?.IcdeSort ?? 999),
  );
};

export const babysRequestParamProcessor = (
  formFieldValues: any,
  ignoreNull?: boolean,
) => {
  let babyIcdes = [];

  let formBabys = cloneDeep(formFieldValues['babies']) ?? [];

  formBabys?.forEach((babyItem, babyIndex) => {
    babyItem['BabySort'] = babyIndex + 1;
    babyItem['UmbilicalCord'] = Array.isArray(babyItem['UmbilicalCord'])
      ? babyItem['UmbilicalCord']?.join(',')
      : babyItem['UmbilicalCord'];

    let currentBabyIcdeTableItems = babyItem?.['icde-table'];
    if (!isEmptyValues(currentBabyIcdeTableItems)) {
      currentBabyIcdeTableItems?.forEach((item, index) => {
        item['BabySort'] = babyItem['BabySort'];
        item['IcdeSort'] = index + 1;

        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        if (ignoreNull) {
          if (!isNil(item['IcdeCode'])) {
            babyIcdes.push(item);
          }
        } else {
          babyIcdes.push(item);
        }
      });
    }
  });

  return {
    babys: formBabys,
    babyIcdes: babyIcdes,
  };
};
