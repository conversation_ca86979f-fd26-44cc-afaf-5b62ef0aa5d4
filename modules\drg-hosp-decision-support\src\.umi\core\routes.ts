// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'C:/Users/<USER>/Desktop/unionnet/dmr/unidmrweb/node_modules/.pnpm/@umijs+runtime@3.5.34_react@16.14.0/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@uni/components/src/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "exact": true,
    "redirect": "/hospLevel/Cmi"
  },
  {
    "path": "/hospLevel",
    "routes": [
      {
        "path": "/hospLevel/Cmi",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__hospLevel__hospCmi__index' */'@/pages/hospLevel/hospCmi/index'), loading: LoadingComponent})
      },
      {
        "path": "/hospLevel/diseaseType",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__hospLevel__diseaseType__index' */'@/pages/hospLevel/diseaseType/index'), loading: LoadingComponent})
      },
      {
        "path": "/hospLevel/groupType",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__hospLevel__groupType__index' */'@/pages/hospLevel/groupType/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/majoePerfDept",
    "routes": [
      {
        "path": "/majoePerfDept/Cmi",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__majoePerfDept__cmi__index' */'@/pages/majoePerfDept/cmi/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/deptLevel",
    "routes": [
      {
        "path": "/deptLevel/Cmi",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__deptLevel__deptCmi__index' */'@/pages/deptLevel/deptCmi/index'), loading: LoadingComponent})
      },
      {
        "path": "/deptLevel/diseaseType",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__deptLevel__deptDiseaseType__index' */'@/pages/deptLevel/deptDiseaseType/index'), loading: LoadingComponent})
      },
      {
        "path": "/deptLevel/groupType",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__deptLevel__groupType__index' */'@/pages/deptLevel/groupType/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/medTeamLevel",
    "routes": [
      {
        "path": "/medTeamLevel/Cmi",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__medTeamLevel__medTeamCmi__index' */'@/pages/medTeamLevel/medTeamCmi/index'), loading: LoadingComponent})
      },
      {
        "path": "/medTeamLevel/diseaseType",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__medTeamLevel__medTeamDiseaseType__index' */'@/pages/medTeamLevel/medTeamDiseaseType/index'), loading: LoadingComponent})
      },
      {
        "path": "/medTeamLevel/groupType",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__medTeamLevel__groupType__index' */'@/pages/medTeamLevel/groupType/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/detail",
    "routes": [
      {
        "path": "/detail/hospDrgs",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__details__hospDrgs__index' */'@/pages/details/hospDrgs/index'), loading: LoadingComponent})
      },
      {
        "path": "/detail/hospOper",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__details__hospOper__index' */'@/pages/details/hospOper/index'), loading: LoadingComponent})
      },
      {
        "path": "/detail/hospSd",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__details__hospSd__index' */'@/pages/details/hospSd/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/difficultCases",
    "routes": [
      {
        "path": "/difficultCases/hosp",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__hospLevel__difficultCase__index' */'@/pages/hospLevel/difficultCase/index'), loading: LoadingComponent})
      },
      {
        "path": "/difficultCases/dept",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__deptLevel__difficultCase__index' */'@/pages/deptLevel/difficultCase/index'), loading: LoadingComponent})
      },
      {
        "path": "/difficultCases/medTeam",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__medTeamLevel__difficultCase__index' */'@/pages/medTeamLevel/difficultCase/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/surgicalAbility",
    "routes": [
      {
        "path": "/surgicalAbility/hosp",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__hospLevel__operRate__index' */'@/pages/hospLevel/operRate/index'), loading: LoadingComponent})
      },
      {
        "path": "/surgicalAbility/dept",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__deptLevel__operRate__index' */'@/pages/deptLevel/operRate/index'), loading: LoadingComponent})
      },
      {
        "path": "/surgicalAbility/medTeam",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__medTeamLevel__operRate__index' */'@/pages/medTeamLevel/operRate/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/sdComposition",
    "routes": [
      {
        "path": "/sdComposition/hosp",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__hospLevel__SdComposition__index' */'@/pages/hospLevel/SdComposition/index'), loading: LoadingComponent})
      },
      {
        "path": "/sdComposition/dept",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__deptLevel__SdComposition__index' */'@/pages/deptLevel/SdComposition/index'), loading: LoadingComponent})
      },
      {
        "path": "/sdComposition/medTeam",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__medTeamLevel__SdComposition__index' */'@/pages/medTeamLevel/SdComposition/index'), loading: LoadingComponent})
      }
    ]
  },
  {
    "path": "/medicalQuality",
    "routes": [
      {
        "path": "/medicalQuality/hosp",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__hospLevel__medicalQuality__index' */'@/pages/hospLevel/medicalQuality/index'), loading: LoadingComponent})
      },
      {
        "path": "/medicalQuality/dept",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__deptLevel__medicalQuality__index' */'@/pages/deptLevel/medicalQuality/index'), loading: LoadingComponent})
      },
      {
        "path": "/medicalQuality/medTeam",
        "exact": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__medTeamLevel__medicalQuality__index' */'@/pages/medTeamLevel/medicalQuality/index'), loading: LoadingComponent})
      }
    ]
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
