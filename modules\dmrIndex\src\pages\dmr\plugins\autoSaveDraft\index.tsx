// DraftPlugin.tsx
import { isEmptyValues } from '@uni/utils/src/utils';
import dayjs from 'dayjs';
import { DraftItem, WorkerMessage, WorkerResponse } from './types';

class DraftPlugin {
  hisId: string = null;
  private worker: Worker | null = null;
  private workerReady: boolean = false;
  private pendingOperations: Array<{
    resolve: (value: any) => void;
    reject: (reason: any) => void;
    message: WorkerMessage;
  }> = [];

  // 防抖保存的定时器
  private saveTimer: NodeJS.Timeout | null = null;
  private readonly DEBOUNCE_DELAY = 1000; // 1秒防抖

  // feat: 保证draft唯一 - 跟踪是否已经清理过其他draft
  private hasCleanedOtherDrafts: boolean = false;

  constructor() {
    this.initWorker();
  }

  private initWorker = async () => {
    try {
      // 检查浏览器是否支持Worker
      if (typeof Worker === 'undefined') {
        console.warn('Web Worker not supported, falling back to localStorage');
        this.fallbackToLocalStorage();
        return;
      }
      
      // 检查是否支持IndexedDB
      if (!window.indexedDB) {
        console.warn('IndexedDB not supported, falling back to localStorage');
        this.fallbackToLocalStorage();
        return;
      }
      
      this.worker = new Worker('/draftWorker.js');
      console.log('Worker initialized:', this.worker)
      // 设置Worker超时检测
      const workerTimeout = setTimeout(() => {
        console.error('Worker initialization timeout');
        this.fallbackToLocalStorage();
      }, 55000); // 55秒超时
      
      this.worker.onmessage = (event: MessageEvent<WorkerResponse>) => {
        const { type, data, error } = event.data;
        
        if (type === 'READY') {
          clearTimeout(workerTimeout);
          this.workerReady = true;
          this.processPendingOperations();
          console.log('Worker initialized successfully');
          return;
        }
        
        if (type === 'ERROR' && error && error.includes('IndexedDB')) {
          console.error('IndexedDB error in worker:', error);
          this.fallbackToLocalStorage();
          return;
        }
        
        // 处理其他消息 - 修复：使用shift()确保FIFO顺序
        const operation = this.pendingOperations.shift();
        if (operation) {
          if (type === 'SUCCESS') {
            operation.resolve(data);
          } else if (type === 'ERROR') {
            operation.reject(new Error(error));
          }
        }
      };
      
      this.worker.onerror = (error) => {
        clearTimeout(workerTimeout);
        console.error('Worker error:', error);
        this.fallbackToLocalStorage();
      };
      
      // 监听Worker意外终止
      this.worker.onmessageerror = (error) => {
        console.error('Worker message error:', error);
        this.fallbackToLocalStorage();
      };
      
    } catch (error) {
      console.error('Failed to initialize worker:', error);
      this.fallbackToLocalStorage();
    }
  };

  private processPendingOperations = () => {
    while (this.pendingOperations.length > 0) {
      const operation = this.pendingOperations[0];
      this.sendMessageToWorker(operation.message)
        .then(operation.resolve)
        .catch(operation.reject);
      this.pendingOperations.shift();
    }
  };

  private sendMessageToWorker = (message: WorkerMessage): Promise<any> => {
    return new Promise((resolve, reject) => {
      // 如果Worker不可用，直接使用localStorage
      if (!this.worker) {
        this.handleFallbackOperation({ resolve, reject, message });
        return;
      }

      if (!this.workerReady) {
        this.pendingOperations.push({ resolve, reject, message });
        return;
      }

      // 添加操作超时机制
      const timeout = setTimeout(() => {
        reject(new Error('Worker operation timeout'));
        // 超时后切换到localStorage模式
        this.fallbackToLocalStorage();
      }, 10000); // 10秒超时

      const originalResolve = resolve;
      const originalReject = reject;

      // 修复：当worker ready时，先发送消息再添加到队列
      try {
        this.worker.postMessage(message);

        // 消息发送成功后才添加到队列等待响应
        this.pendingOperations.push({
          resolve: (data) => {
            clearTimeout(timeout);
            originalResolve(data);
          },
          reject: (error) => {
            clearTimeout(timeout);
            originalReject(error);
          },
          message
        });
      } catch (error) {
        clearTimeout(timeout);
        console.error('Failed to send message to worker:', error);
        this.fallbackToLocalStorage();
        this.handleFallbackOperation({ resolve: originalResolve, reject: originalReject, message });
      }
    });
  };

  private fallbackToLocalStorage = () => {
    console.warn('Using localStorage fallback mode');
    this.worker = null;
    this.workerReady = false;
    
    // 清理等待中的操作，全部使用localStorage处理
    while (this.pendingOperations.length > 0) {
      const operation = this.pendingOperations.shift();
      if (operation) {
        this.handleFallbackOperation(operation);
      }
    }
  };

  private handleFallbackOperation = async (operation: any) => {
    try {
      const { type, payload } = operation.message;
      let result: any;

      switch (type) {
        case 'SAVE_DRAFT':
          const draftItem = {
            content: payload.content,
            timestamp: Date.now(),
          };
          localStorage.setItem(this.getDraftKey(), JSON.stringify(draftItem));
          result = draftItem;
          break;

        case 'GET_DRAFT':
          const draftString = localStorage.getItem(this.getDraftKey()) ?? '{}';
          result = JSON.parse(draftString);
          break;

        case 'DELETE_DRAFT':
          localStorage.removeItem(this.getDraftKey());
          result = true;
          break;

        case 'VALIDATE_DRAFT':
          const currentDraft = JSON.parse(localStorage.getItem(this.getDraftKey()) ?? '{}');
          if (currentDraft.content && currentDraft.timestamp) {
            const latestTimestamp = new Date(payload.dmrLatestDate).getTime();
            if (latestTimestamp >= currentDraft.timestamp) {
              localStorage.removeItem(this.getDraftKey());
            }
          }
          result = true;
          break;

        // feat: 保证draft唯一 - localStorage模式下清除其他draft
        case 'CLEAR_OTHER_DRAFTS':
          const currentHisId = payload.currentHisId;
          const keysToRemove: string[] = [];

          // 遍历localStorage找到所有draft相关的key
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('draft-dmr-') && key !== `draft-dmr-${currentHisId}`) {
              keysToRemove.push(key);
            }
          }

          // 删除其他draft
          keysToRemove.forEach(key => {
            localStorage.removeItem(key);
          });

          result = true;
          break;

        default:
          throw new Error('Unknown operation type');
      }

      operation.resolve(result);
    } catch (error) {
      operation.reject(error);
    }
  };

  setHisId(hisId: string) {
    // feat: 保证draft唯一 - 当hisId变化时重置清理标志
    if (this.hisId !== hisId) {
      this.hasCleanedOtherDrafts = false;
    }
    this.hisId = hisId;
  }

  getDraftKey = () => {
    return `draft-dmr-${this.hisId}`;
  };

  // feat: 保证draft唯一 - 清除除当前hisId外的所有其他draft
  private clearOtherDrafts = async (): Promise<void> => {
    if (!this.hisId) return;

    try {
      await this.sendMessageToWorker({
        type: 'CLEAR_OTHER_DRAFTS',
        payload: { currentHisId: this.hisId }
      });
    } catch (error) {
      console.error('Failed to clear other drafts:', error);
      // 降级到localStorage清理
      const keysToRemove: string[] = [];

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('draft-dmr-') && key !== this.getDraftKey()) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
    }
  };

  deleteDraft = async (): Promise<void> => {
    if (!this.hisId) return;
    
    try {
      await this.sendMessageToWorker({
        type: 'DELETE_DRAFT',
        payload: { hisId: this.hisId }
      });
    } catch (error) {
      console.error('Failed to delete draft:', error);
      // 降级到localStorage
      localStorage.removeItem(this.getDraftKey());
    }
  };

  getDraft = async (): Promise<DraftItem | any> => {
    if (!this.hisId) return {};
    
    try {
      const result = await this.sendMessageToWorker({
        type: 'GET_DRAFT',
        payload: { hisId: this.hisId }
      });
      return result || {};
    } catch (error) {
      console.error('Failed to get draft:', error);
      // 降级到localStorage
      const draftString = localStorage.getItem(this.getDraftKey()) ?? '{}';
      return JSON.parse(draftString);
    }
  };

  // 立即保存（内部使用）
  private setDraft = async (content: any): Promise<void> => {
    if (!this.hisId) return;

    // feat: 保证draft唯一 - 在第一次保存时清除其他draft
    if (!this.hasCleanedOtherDrafts) {
      await this.clearOtherDrafts();
      this.hasCleanedOtherDrafts = true;
    }

    try {
      await this.sendMessageToWorker({
        type: 'SAVE_DRAFT',
        payload: { hisId: this.hisId, content }
      });
    } catch (error) {
      console.error('Failed to save draft:', error);
      // 降级到localStorage
      const draftItem = {
        content,
        timestamp: Date.now(),
      };
      localStorage.setItem(this.getDraftKey(), JSON.stringify(draftItem));
    }
  };

  // 防抖保存到本地
  saveDraftToLocal = (content: any): void => {
    // 清除之前的定时器
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
    }
    
    // 设置新的防抖定时器
    this.saveTimer = setTimeout(() => {
      this.setDraft(content);
      this.saveTimer = null;
    }, this.DEBOUNCE_DELAY);
  };

  // 立即保存（用于特殊场景，如页面卸载）
  saveImmediately = async (content: any): Promise<void> => {
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
      this.saveTimer = null;
    }
    await this.setDraft(content);
  };

  validateDraft = async (dmrLatestDate: string): Promise<void> => {
    if (!this.hisId) return;
    
    try {
      await this.sendMessageToWorker({
        type: 'VALIDATE_DRAFT',
        payload: { hisId: this.hisId, dmrLatestDate }
      });
    } catch (error) {
      console.error('Failed to validate draft:', error);
      // 降级到localStorage验证
      const draftItem = await this.getDraft();
      
      if (!isEmptyValues(draftItem)) {
        if (isEmptyValues(draftItem?.content) || isEmptyValues(draftItem?.timestamp)) {
          localStorage.removeItem(this.getDraftKey());
          return;
        }

        const latestTimestamp = dayjs(dmrLatestDate).unix() * 1000; // 转换为毫秒
        if (latestTimestamp >= draftItem?.timestamp) {
          localStorage.removeItem(this.getDraftKey());
        }
      }
    }
  };

  // 销毁方法，清理资源
  destroy = () => {
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
      this.saveTimer = null;
    }

    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    this.workerReady = false;
    this.pendingOperations = [];
    // feat: 保证draft唯一 - 重置清理标志
    this.hasCleanedOtherDrafts = false;
  };
}

export default DraftPlugin;