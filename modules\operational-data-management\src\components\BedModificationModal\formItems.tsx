import dayjs from 'dayjs';

// 批量修改床位数的基础表单项
export const bulkFormItems = [
  {
    name: 'ApprovedBedsNumber',
    title: '核定床位数',
    dataType: 'Integer',
    required: false,
    visible: true,
    min: 0,
  },
  {
    name: 'SuppliedBedsNumber',
    title: '开放床位数',
    dataType: 'Integer',
    required: false,
    visible: true,
    min: 0,
  },
];

// 批量修改床位表单项
export const BulkChangeFormItems = (hospList, deptList) => [
  {
    name: 'DateRange',
    title: '时间',
    dataType: 'dateRange',
    colProps: { span: 24 },
    rules: [{ required: true }],
    transform: (values) => {
      return {
        Sdate: values ? dayjs(values[0]).format('YYYY-MM-DD') : undefined,
        Edate: values ? dayjs(values[1]).format('YYYY-MM-DD') : undefined,
      };
    },
    fieldProps: {
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
    },
  },
  {
    name: 'HospCode',
    title: '院区',
    dataType: 'select',
    rules: [{ required: true }],
    opts: hospList,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
    visible: true,
    colProps: { span: 24 },
  },
  {
    name: 'DeptCode',
    title: '科室',
    dataType: 'select',
    rules: [{ required: true }],
    opts: deptList,
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 'responsive',
    },
    visible: true,
    colProps: { span: 24 },
  },
  ...bulkFormItems?.map((d) => {
    return {
      ...d,
      dataType:
        d?.dataType === 'Integer' || d?.dataType === 'Number'
          ? 'number'
          : 'text',
      fieldProps: {
        precision: d?.dataType === 'Integer' ? 0 : 2,
      },
      min: 0,
      colProps: { span: 12 },
    };
  }),
];
