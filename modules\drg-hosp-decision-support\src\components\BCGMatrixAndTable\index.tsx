import { Col, Row, Space, Select } from 'antd';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import CustomGradientChart from '../customGradientChart/index';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import CardWithBtns from '@uni/components/src/cardWithBtns';
import './index.less';
import SingleList from '../SingleList/index';
import { useDeepCompareEffect } from 'ahooks';

export interface IGradientChartAndTableAndPieProps {
  title?: string; // charts & 导出 title
  tableTitle?: string; // table title 没填用 title
  type?: string; // 页面类型1，用于判断selectedItem内容如何入参调api。有：dept majorPerfDept drg adrg medTeam，
  axisOpts?: any; // 象限图的x,y轴的下拉框opts
  defaultAxisOpt?: any; // 象限图的x,y轴的默认值
  category?: string; // 象限图的category
  listValueKey?: string;
  clickable?: boolean; // table能不能点击下钻
  emitter?: string; // table点击事件触发的eventconstants 不填默认是EventConstant.TABLE_ROW_CLICK
  mouseEvent?: any; // 象限图的mouseEvent
  args?: {
    type?: string; // 页面类型2，用于判断globalState内容如何入参调api。有 dept medTeam majorPerfDept
    api?: string;
    extraApiArgs?: any; // 额外入参
    columns?: any[]; // 本地columns
    ref?: any; // react useRef
    detailsTitle?: string; // 目前无用
  };
  detailAction?: (record: any) => void;
}

const BCGMatrixAndTable = (props: IGradientChartAndTableAndPieProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, MajorPerfDepts, CliDepts, MedTeams } =
    globalState?.searchParams;

  const [tableParams, setTableParams] = useState(undefined);

  const [dataSource, setDataSource] = useState([]);

  const [filteredTableData, setFilteredTableData] = useState<any[]>([]);

  const [selectedTableItem, setSelectedTableItem] = useState(undefined);

  const [searchValue, setSearchValue] = useState(undefined);

  const [axisOpts, setAxisOpts] = useState({
    total: [],
    default: {},
  });

  // 矩阵图x轴、y轴下拉框
  const [quadrantSelectValue, setQuadrantSelectValue] = useState({
    xAxis: { label: '平均住院天数', value: 'AvgInPeriod' },
    yAxis: { label: '均次费用', value: 'AvgTotalFee' },
  });

  // Columns
  const {
    data: ColumnsData,
    loading: getColumnsLoading,
    mutate: mutateColumns,
    run: getColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(props?.args?.api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor(
            props?.args?.columns,
            res.data?.Columns,
          );
        }
      },
    },
  );

  // 获取数据
  const { loading: getDataSourceLoading, run: getDataSourceReq } = useRequest(
    (data) =>
      uniCommonService(props?.args?.api, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 做容错 有些返回的用Stats包裹，有些则是直接用arr
          setDataSource(res?.data?.Stats ?? res?.data);
          // 还要判断
          if (
            props?.args?.columns &&
            props?.args?.columns?.findIndex(
              (d) => d?.defaultSortOrder === 'descend',
            ) > -1
          ) {
            // 同时给一个值 因为排序了..
            setSelectedTableItem(
              _.maxBy(res?.data?.Stats ?? res?.data, 'PatCnt'),
            );
          } else {
            setSelectedTableItem(res.data.Stats?.at(0) ?? res?.data?.at(0));
          }
          // 目前没用到
          return res.data;
        }
      },
    },
  );

  // table click
  useEffect(() => {
    // 多tab共存情况下 调用该组件页面 自行在传emitter的时候 保证唯一
    if (props?.emitter) {
      Emitter.on(props?.emitter, ({ record, index }) => {
        setSelectedTableItem(record);
      });
      return () => {
        Emitter.off(props?.emitter);
      };
    } else {
      Emitter.on(EventConstant.TABLE_ROW_CLICK, ({ record, index }) => {
        // 没 type的情况 只有一个默认emitter
        setSelectedTableItem(record);
      });
      return () => {
        Emitter.off(EventConstant.TABLE_ROW_CLICK);
      };
    }
  }, [selectedTableItem, props?.type]);

  // 处理tableParams
  useDeepCompareEffect(() => {
    // if (dateRange?.length && hospCodes?.length) {
    if (dateRange?.length) {
      let tableParams: any = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
      };
      if (props?.type === 'dept') {
        if (CliDepts?.length) {
          tableParams = {
            ...tableParams,
            CliDepts,
          };
        }
      } else if (props?.type === 'medTeam') {
        if (MedTeams?.length) {
          tableParams = {
            ...tableParams,
            MedTeams,
          };
        }
      } else if (props?.type === 'majorPerfDept') {
        if (MajorPerfDepts?.length) {
          tableParams = {
            ...tableParams,
            MajorPerfDepts,
          };
        }
      }
      setTableParams(tableParams);
    }
  }, [dateRange, hospCodes, MajorPerfDepts, CliDepts, MedTeams, props?.type]);

  // columns
  useEffect(() => {
    if (props?.args?.api) {
      if (!ColumnsData?.length) getColumnsReq();
    }
  }, [props?.args?.api]);

  // data
  useEffect(() => {
    console.log('tableParams123456', tableParams);
    if (tableParams) {
      console.log('tableParams12345677', tableParams);
      getDataSourceReq({ ...tableParams, ...props?.args?.extraApiArgs });
    }
  }, [tableParams, props?.args?.extraApiArgs]);

  // handleSearch
  const handleSearch = (value) => {
    setSearchValue(value);
  };

  // deal axis
  useEffect(() => {
    if (props?.axisOpts && props?.defaultAxisOpt && ColumnsData?.length) {
      if (Object.keys(props?.defaultAxisOpt)?.length) {
        //
        setQuadrantSelectValue({
          xAxis: {
            label: ColumnsData?.find(
              (column) => column?.data === props?.defaultAxisOpt.xAxis,
            )?.title,
            value: props?.defaultAxisOpt.xAxis,
          },
          yAxis: {
            label: ColumnsData?.find(
              (column) => column?.data === props?.defaultAxisOpt.yAxis,
            )?.title,
            value: props?.defaultAxisOpt.yAxis,
          },
        });
      }

      setAxisOpts({
        total: props?.axisOpts?.map((value) => ({
          label: ColumnsData?.find((column) => column?.data === value)?.title,
          value: value,
        })),
        default: {
          xAxis: {
            label: ColumnsData?.find(
              (column) => column?.data === props?.defaultAxisOpt.xAxis,
            )?.title,
            value: props?.defaultAxisOpt.xAxis,
          },
          yAxis: {
            label: ColumnsData?.find(
              (column) => column?.data === props?.defaultAxisOpt.yAxis,
            )?.title,
            value: props?.defaultAxisOpt.yAxis,
          },
        },
      });
    }
  }, [props?.axisOpts, props?.defaultAxisOpt, ColumnsData]);

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={24} md={14} lg={14} xl={16}>
        <CardWithBtns
          content={
            // 注意：这里有多个echarts需要点击的话 因为也在tab下，所以也应该传一个emitter进来
            <CustomGradientChart
              id={`${props?.args?.type || ''}${
                props?.type || ''
              }BubbleGradient`}
              height={550}
              category={props?.category}
              axisOpts={axisOpts?.total}
              defaultAxisOpt={axisOpts?.default}
              loading={getDataSourceLoading}
              dataSource={dataSource}
              mouseEvents={props?.mouseEvent}
              quadrantSelectValue={quadrantSelectValue}
              setQuadrantSelectValue={setQuadrantSelectValue}
            />
          }
          extra={
            <Space>
              <div className="card-extra-quadrant">
                <label>x轴：</label>
                <Select
                  style={{ width: '150px' }}
                  className="quadrant-axis-select"
                  labelInValue
                  options={axisOpts?.total?.filter(
                    (d) =>
                      d.value !== quadrantSelectValue.xAxis.value &&
                      d.value !== quadrantSelectValue.yAxis.value,
                  )}
                  value={quadrantSelectValue.xAxis}
                  onChange={(value) =>
                    setQuadrantSelectValue({
                      ...quadrantSelectValue,
                      xAxis: value,
                    })
                  }
                />
              </div>
              <div className="card-extra-quadrant">
                <label>y轴：</label>
                <Select
                  style={{ width: '150px' }}
                  className="quadrant-axis-select"
                  labelInValue
                  options={axisOpts?.total?.filter(
                    (d) =>
                      d.value !== quadrantSelectValue.yAxis.value &&
                      d.value !== quadrantSelectValue.xAxis.value,
                  )}
                  value={quadrantSelectValue.yAxis}
                  onChange={(value) =>
                    setQuadrantSelectValue({
                      ...quadrantSelectValue,
                      yAxis: value,
                    })
                  }
                />
              </div>
            </Space>
          }
          needExport={true}
          exportTitle={props?.title}
          exportData={dataSource}
          exportColumns={ColumnsData}
          needModalDetails={true}
          onRefresh={() => {
            getDataSourceReq({ ...tableParams, ...props?.args?.extraApiArgs });
          }}
        />
      </Col>
      <Col xs={24} sm={24} md={10} lg={10} xl={8}>
        <CardWithBtns
          content={
            <SingleList
              loading={getDataSourceLoading}
              columns={ColumnsData}
              data={_.map(
                _.orderBy(dataSource, props?.listValueKey || 'Cmi', 'desc'),
                (data, i) => {
                  return {
                    key: i + 1,
                    name: data?.[props?.category],
                    value: props?.listValueKey
                      ? data?.[props?.listValueKey]
                      : data?.Cmi?.toFixed(4),
                    ...data,
                  };
                },
              )}
              listExtraAction={props?.detailAction}
            ></SingleList>
          }
          needExport={true}
          exportTitle={props?.tableTitle || props?.title}
          exportData={_.orderBy(
            dataSource,
            props?.listValueKey || 'Cmi',
            'desc',
          )}
          exportColumns={ColumnsData}
          needModalDetails={true}
          onRefresh={() => {
            getDataSourceReq({ ...tableParams, ...props?.args?.extraApiArgs });
          }}
          columnsEditableUrl={props?.args?.api}
          onColumnChange={(newColumns) => {
            mutateColumns(
              tableColumnBaseProcessor(props?.args?.columns, newColumns),
            );
          }}
        />
      </Col>
    </Row>
  );
};

export default BCGMatrixAndTable;
